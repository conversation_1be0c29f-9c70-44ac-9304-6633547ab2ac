---
apiVersion: v1
kind: ConfigMap
metadata:
  name: backend-config
  namespace: llm-eval
data:
  DATABASE_HOST: "postgresql-service"
  DATABASE_PORT: "5432"
  DATABASE_NAME: "llm_eval"
  # OPENROUTER_API_KEY is sensitive, should remain in secret

---
apiVersion: v1
kind: Secret
metadata:
  name: backend-secret
  namespace: llm-eval
type: Opaque
data:
  # Replace with your actual base64 encoded values during CI/CD
  DATABASE_USER: cG9zdGdyZXM= # postgres
  DATABASE_PASSWORD: cG9zdGdyZXMxMjM= # postgres123
  OPENROUTER_API_KEY: c2stb3ItdjEtMjM5ZTcxNDJiNzYzZjBkOGY3ZTc2ZjE5YTc1MWNhNzAzOTBlN2YzNGVmYTY5ZDllMzVkMzE0Mjg2YTRlNjE0OQ==
  DATABASE_URL: cG9zdGdyZXNxbCthc3luY3BnOi8vcG9zdGdyZXM6cG9zdGdyZXMxMjNAcG9zdGdyZXNxbC1zZXJ2aWNlOjU0MzIvbGxtX2V2YWw= # Placeholder, will be constructed and replaced by CI

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: backend
  namespace: llm-eval
  labels:
    app: backend
spec:
  replicas: 2
  selector:
    matchLabels:
      app: backend
  template:
    metadata:
      labels:
        app: backend
    spec:
      containers:
      - name: backend
        image: llm-eval-backend:latest
        imagePullPolicy: Never
        ports:
        - containerPort: 8000
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: backend-secret
              key: DATABASE_URL
        - name: OPENROUTER_API_KEY
          valueFrom:
            secretKeyRef:
              name: backend-secret
              key: OPENROUTER_API_KEY
        # Individual DB components are no longer needed here as DATABASE_URL is fully formed
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /api/v1/health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /api/v1/health
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
        volumeMounts:
        - name: logs
          mountPath: /app/logs
      volumes:
      - name: logs
        emptyDir: {}

---
apiVersion: v1
kind: Service
metadata:
  name: backend-service
  namespace: llm-eval
  labels:
    app: backend
spec:
  selector:
    app: backend
  ports:
  - port: 8000
    targetPort: 8000
    protocol: TCP
  type: ClusterIP 