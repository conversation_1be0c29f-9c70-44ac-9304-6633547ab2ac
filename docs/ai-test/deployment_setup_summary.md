# LLM评估平台部署设置总结

**日期**: 2025-05-29  
**目标**: 为不同AI工具创建独立的测试环境，比较它们的能力

---

## 📋 项目背景

**核心目标**: 测试和比较不同AI代码助手工具的能力：
- Augment Code
- Roo Code  
- Cline
- Cursor
- GitHub Copilot
- Windsurf

**测试策略**: 为每个工具创建独立的数据库实例，所有数据库都降级到同一个baseline版本（`28b5937d928a`），确保公平比较。

---

## ✅ 今天完成的工作

### 1. **GitHub Self-hosted Runner设置**
- **问题**: GitHub Actions默认在云端运行，无法访问本地k3s集群
- **解决方案**: 配置Self-hosted Runner在本地WSL2环境中运行
- **步骤**:
  ```bash
  # 下载并配置GitHub Runner
  curl -o actions-runner-linux-x64-2.324.0.tar.gz -L https://github.com/actions/runner/releases/download/v2.324.0/actions-runner-linux-x64-2.324.0.tar.gz
  tar xzf ./actions-runner-linux-x64-2.324.0.tar.gz
  ./config.sh --url https://github.com/everestaiteam2025/llm-eval-platform --token BR72KOXLNIFHFJBKNODJ2VTIHC7IE
  ```
- **结果**: ✅ Runner成功连接到GitHub，监听任务

### 2. **Kubernetes集群验证**
- **环境**: Windows Rancher Desktop + WSL2 Ubuntu
- **验证步骤**:
  ```bash
  kubectl cluster-info
  kubectl get nodes
  kubectl get namespaces
  ```
- **结果**: ✅ k3s集群运行正常

### 3. **PostgreSQL数据库部署**
- **部署资源**:
  - Namespace: `llm-eval`
  - PersistentVolumeClaim: `postgresql-pvc` (10Gi)
  - Secret: `postgresql-secret` (用户名/密码)
  - Deployment: `postgresql` (postgres:15-alpine)
  - Service: `postgresql-service` (ClusterIP)
- **验证**:
  ```bash
  kubectl run psql-test --rm -it --image=postgres:15-alpine --restart=Never --namespace=llm-eval -- psql *********************************************************/llm_eval -c "SELECT version();"
  ```
- **结果**: ✅ PostgreSQL 15.12正常运行，网络连接正常

### 4. **Docker镜像构建和优化**

#### Backend镜像问题解决
- **问题**: Poetry虚拟环境配置导致多阶段构建失败
- **解决方案**: 简化为单阶段构建，使用系统Python
- **关键配置**:
  ```dockerfile
  ENV POETRY_VIRTUALENVS_CREATE=false
  RUN poetry install --only=main --no-root
  ```
- **结果**: ✅ Backend镜像构建成功

#### Frontend镜像问题解决
- **问题**: nginx权限错误 - "Permission denied" for `/run/nginx.pid`
- **解决方案**: 
  - 修改`nginx.conf`: `pid /tmp/nginx.pid;`
  - 调整Dockerfile权限设置
- **结果**: ✅ Frontend镜像构建成功

### 5. **数据库迁移配置修复**
- **问题**: Alembic使用硬编码的localhost数据库URL
- **解决方案**: 修改`alembic/env.py`优先使用环境变量
- **代码修改**:
  ```python
  # Before
  db_url = config.get_main_option("sqlalchemy.url")
  
  # After  
  db_url = os.environ.get("DATABASE_URL") or config.get_main_option("sqlalchemy.url")
  ```
- **结果**: ✅ 成功运行所有迁移到最新版本

### 6. **完整应用栈部署**
- **组件状态**:
  - PostgreSQL: ✅ 运行正常
  - Backend (2 replicas): ✅ 运行正常，API响应正常
  - Frontend (2 replicas): ✅ 运行正常，nginx配置正确
- **服务暴露**:
  - Frontend: LoadBalancer `*************:3000`
  - Backend: ClusterIP (内部访问)
  - PostgreSQL: ClusterIP (内部访问)

### 7. **健康检查验证**
```json
{
  "status": "healthy",
  "database": {
    "connection": {"status": "healthy"},
    "tables": {"status": "healthy", "message": "Tasks table accessible (0 tasks)"}
  },
  "services": {
    "api": {"status": "healthy"}
  }
}
```

### 8. **本地Docker Registry部署** ✅ **NEW**
- **目标**: 解决CI/CD推送到GitHub Registry的网络瓶颈问题
- **实现**:
  - 创建本地Docker Registry (`localhost:5000`)
  - 修改CI/CD pipeline使用本地registry
  - 配置k3s信任本地registry
  - 创建registry管理和测试脚本
- **性能提升**:
  - 构建+推送速度提升7-10倍
  - 从~450秒降至~60秒
- **工具**:
  - `scripts/manage-local-registry.sh` - Registry管理
  - `scripts/test-local-registry.sh` - 完整测试流程
  - `docs/LOCAL_REGISTRY.md` - 详细文档

### 9. **多实例PostgreSQL环境** ✅ **NEW**
- **目标**: 为每个AI工具创建独立的数据库实例
- **实现**:
  - 部署6个独立的PostgreSQL实例
  - 每个实例有独立的PVC、Secret、Service
  - 数据库命名: `llm_eval_augment`, `llm_eval_roo`, `llm_eval_cline`, `llm_eval_cursor`, `llm_eval_copilot`, `llm_eval_windsurf`
- **配置文件**: `k8s/postgresql-multi-instances.yaml`
- **管理脚本**: `scripts/setup-multi-databases.sh`

---

## 🐛 遇到的主要问题和解决方案

### 1. **Poetry虚拟环境问题**
- **错误**: `COPY --from=builder /app/.venv /app/.venv: not found`
- **原因**: `POETRY_VENV_IN_PROJECT=1`没有正确创建.venv目录
- **解决**: 使用`POETRY_VIRTUALENVS_CREATE=false`直接安装到系统Python

### 2. **Nginx权限问题**
- **错误**: `nginx: [emerg] open() "/run/nginx.pid" failed (13: Permission denied)`
- **原因**: 非root用户无法写入`/run`目录
- **解决**: 将PID文件改到`/tmp/nginx.pid`并设置正确权限

### 3. **数据库连接问题**
- **错误**: Alembic连接到localhost而不是postgresql-service
- **原因**: `alembic.ini`中硬编码的数据库URL
- **解决**: 修改`env.py`优先使用环境变量`DATABASE_URL`

### 4. **容器镜像拉取策略**
- **问题**: Kubernetes尝试从远程拉取本地构建的镜像
- **解决**: 设置`imagePullPolicy: Never`使用本地镜像

---

## 📊 当前状态

### 技术栈版本
- **Kubernetes**: k3s v1.31.5 (Rancher Desktop)
- **PostgreSQL**: 15.12 
- **Python**: 3.12
- **Node.js**: 20
- **数据库Schema**: `d70dbb5a6738` (最新版本)

### 部署架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │    Backend      │    │   PostgreSQL    │
│   (nginx)       │───▶│   (FastAPI)     │───▶│   (postgres)    │
│   Port: 3000    │    │   Port: 8000    │    │   Port: 5432    │
│   LoadBalancer  │    │   ClusterIP     │    │   ClusterIP     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 资源配置
- **命名空间**: `llm-eval`
- **持久化存储**: 10Gi (local-path)
- **副本数**: Frontend(2), Backend(2), PostgreSQL(1)

---

## 🚀 下一步计划

### 1. **数据库基线设置** ⏳ **IN PROGRESS**
- [x] 为每个AI工具创建独立的PostgreSQL实例 ✅ **COMPLETED**
- [x] 配置数据库命名规范 ✅ **COMPLETED**
- [x] 对所有测试数据库执行`alembic downgrade 28b5937d928a`
- [x] 验证所有数据库schema一致性

### 2. **本地开发环境优化** ✅ **COMPLETED**
- [x] 部署本地Docker Registry ✅ **COMPLETED**
- [x] 修改CI/CD pipeline使用本地registry ✅ **COMPLETED**
- [x] 创建registry管理工具 ✅ **COMPLETED**
- [x] 性能测试和文档 ✅ **COMPLETED**

### 3. **GitHub Actions集成测试**
- [x] 测试Self-hosted Runner的CI/CD流程 ✅ **COMPLETED**
- [x] 配置本地registry集成 ✅ **COMPLETED**

### 4. **AI工具测试**
- [x] 设计标准化测试任务（参考Usage Statistics功能实现挑战）
- [x] 为每个AI工具准备独立的分支

---

## 📝 配置文件清单

### Kubernetes配置
- `k8s/namespace.yaml` - 命名空间定义
- `k8s/postgresql.yaml` - PostgreSQL部署配置
- `k8s/backend.yaml` - Backend应用配置  
- `k8s/frontend.yaml` - Frontend应用配置

### Docker配置
- `backend/Dockerfile` - Backend镜像构建
- `frontend/Dockerfile` - Frontend镜像构建
- `frontend/nginx.conf` - Nginx配置

### CI/CD配置
- `.github/workflows/ci-cd.yaml` - GitHub Actions工作流(已修改为使用self-hosted runner)

### 数据库配置
- `backend/alembic.ini` - Alembic配置
- `backend/alembic/env.py` - Alembic环境配置(已修改支持环境变量)

---

## 🔧 有用的命令

### Kubernetes操作
```bash
# 查看所有资源状态
kubectl get all -n llm-eval

# 查看Pod日志
kubectl logs -l app=backend -n llm-eval --tail=50

# 执行数据库迁移
kubectl exec -n llm-eval deployment/backend -- poetry run alembic upgrade head

# 测试应用健康状态
kubectl exec -n llm-eval deployment/backend -- curl -s http://localhost:8000/api/v1/health
```

### Docker操作
```bash
# 构建镜像
docker build -t llm-eval-backend:latest ./backend
docker build -t llm-eval-frontend:latest ./frontend

# 查看镜像
docker images | grep llm-eval
```

### GitHub Runner
```bash
# 检查Runner状态
ps aux | grep Runner.Listener

# 重启Runner (如果需要)
cd /home/<USER>/actions-runner && ./run.sh
```

---

## 💡 经验总结

1. **容器化权限管理**: 非root用户运行服务时要特别注意文件权限和目录访问权限
2. **Kubernetes网络**: 服务间通信使用Service名称而不是localhost
3. **环境变量优先级**: 应用配置应优先使用环境变量，便于容器化部署
4. **镜像拉取策略**: 本地开发时使用`imagePullPolicy: Never`避免远程拉取
5. **数据库迁移**: 确保迁移工具使用与应用相同的数据库连接配置

---

**状态**: 🟢 基础部署环境已就绪，可以开始AI工具测试 