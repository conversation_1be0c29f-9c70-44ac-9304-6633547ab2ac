# AI工具测试任务：Usage Statistics功能实现


**任务性质**: 复杂的全栈功能开发编码挑战 (Coding Challenge)
**技术栈**: FastAPI + React + PostgreSQL + Alembic
**难度等级**: ⭐⭐⭐⭐⭐ (高级)
**用途**: 作为AI工具在复杂软件开发任务中能力的评估基准，特别关注代码生成、系统理解、API集成、数据库操作和全栈问题的解决能力。本任务源于一个更广泛的项目，旨在探索和评估AI在软件开发全生命周期中的应用潜力。

---
## 📄 项目背景与愿景 (摘录)

本项目旨在研究和部署AI以开发和验证程序，探索如何利用AI改进软件开发的各个方面。目标是让AI能够快速迭代以达到可工作的解决方案，并评估AI在设计、构建、测试、文档化等环节的效能。此"Usage Statistics功能实现"任务是该项目下设定的具体编码挑战之一，用于衡量不同AI工具在实际开发场景中的表现。

---

## 📋 任务描述

**功能需求**: 为LLM评估平台添加完整的使用统计功能，跟踪AI模型在**generations**和**evaluations**中的token使用量、成本和其他指标。

**参考文档**: [OpenRouter Usage Accounting](https://openrouter.ai/docs/usage-accounting)

**核心要求**:
1. 后端集成OpenRouter usage tracking（generations + evaluations）
2. 数据库存储usage statistics（两个表都需要）
3. 前端显示使用统计信息（个别和聚合）
4. 支持evaluations中的usage统计
5. 创建完整的数据库迁移链 (AI应尝试一次性正确创建所有字段和类型)

---

## 🎯 作为编码挑战的测试目标

本编码挑战旨在测试AI工具在以下方面的能力：
- **整体规划与理解**: AI是否能理解整个任务需求，并规划出一个可行的全栈实现方案。
- **API集成能力**: AI集成第三方API (OpenRouter) 的准确性和效率。
- **数据库设计与一次性迁移**: AI能否正确设计数据模型并通过一次Alembic迁移完成数据库更改。
- **全栈开发**: AI同时处理后端API、前端UI和数据库Schema的能力。
- **问题预见与解决**: AI在一次性生成大量代码时，如何处理潜在的依赖关系和技术细节。
- **代码质量与一致性**: 生成代码的正确性、可读性、可维护性，以及在generations和evaluations模块间的一致性。

这些测试目标与更广泛项目愿景中的"代码生成质量"、"代码库上下文感知能力"、"调试辅助"等评估标准直接相关。

---

## ✅ 基准实现过程 (手动 + Cursor辅助)

以下是人类开发者（Rui Tao）在Cursor辅助下完成此任务的详细步骤。此过程可作为AI工具表现的基准参考。**值得注意的是，基准实现中的数据库迁移分了三步，这是因为最初对OpenRouter返回的`cost_credits`类型不确定，以及逐步添加功能的过程。AI工具在理想情况下，应能通过更全面的分析一次性完成数据库字段的正确添加。**

### 第一阶段：后端数据模型扩展

#### 1. **Generation模型字段添加**
```python
# backend/app/db/models/generation.py
class Generation(Base):
    # ... existing fields ...
    
    # Usage statistics fields
    prompt_tokens = Column(Integer, nullable=True)
    completion_tokens = Column(Integer, nullable=True) 
    total_tokens = Column(Integer, nullable=True)
    reasoning_tokens = Column(Integer, nullable=True)
    cached_tokens = Column(Integer, nullable=True)
    cost_credits = Column(Float, nullable=True)  # 注意：最初为Integer，后来修正为Float
    generation_id = Column(String, nullable=True)
```

#### 2. **Ranking模型字段添加**
```python
# backend/app/db/models/ranking.py
class Ranking(Base):
    # ... existing fields ...
    
    # Usage statistics fields for evaluations
    prompt_tokens = Column(Integer, nullable=True)
    completion_tokens = Column(Integer, nullable=True)
    total_tokens = Column(Integer, nullable=True)
    reasoning_tokens = Column(Integer, nullable=True)
    cached_tokens = Column(Integer, nullable=True)
    cost_credits = Column(Float, nullable=True)
    generation_id = Column(String, nullable=True)
```

#### 3. **Pydantic Schema更新**
```python
# backend/app/schemas/generation.py
class GenerationBase(BaseModel):
    # ... existing fields ...
    prompt_tokens: Optional[int] = None
    completion_tokens: Optional[int] = None
    total_tokens: Optional[int] = None
    reasoning_tokens: Optional[int] = None
    cached_tokens: Optional[int] = None
    cost_credits: Optional[float] = None
    generation_id: Optional[str] = None

# backend/app/schemas/ranking.py 
class RankingBase(BaseModel):
    # ... existing fields ...
    prompt_tokens: Optional[int] = None
    completion_tokens: Optional[int] = None
    total_tokens: Optional[int] = None
    reasoning_tokens: Optional[int] = None
    cached_tokens: Optional[int] = None
    cost_credits: Optional[float] = None
    generation_id: Optional[str] = None
```

#### 4. **LLM服务修改**
```python
# backend/app/services/llm_service.py
async def generate_response(self, messages: List[Dict], model: str = None):
    try:
        response = await self.client.chat.completions.create(
            model=model or self.default_model,
            messages=messages,
            extra_body={
                "usage": {"include": True}  # 启用OpenRouter usage tracking
            }
        )
        
        # 提取usage statistics (通用函数)
        usage_stats = self._extract_usage_stats(response)
        return response, usage_stats

def _extract_usage_stats(self, response):
    """通用的usage统计提取函数"""
    usage_stats = {}
    if hasattr(response, 'usage') and response.usage:
        usage_stats = {
            'prompt_tokens': getattr(response.usage, 'prompt_tokens', None),
            'completion_tokens': getattr(response.usage, 'completion_tokens', None),
            'total_tokens': getattr(response.usage, 'total_tokens', None),
            'reasoning_tokens': getattr(response.usage, 'reasoning_tokens', None),
            'cached_tokens': getattr(response.usage, 'cached_tokens', None),
            'cost_credits': getattr(response.usage, 'cost_credits', None),
        }
    
    # 提取generation_id
    if hasattr(response, 'id'):
        usage_stats['generation_id'] = response.id
    
    return usage_stats
```

#### 5. **Evaluation服务更新**
```python
# backend/app/services/evaluation_service.py
async def evaluate_generations(self, task_id: int, evaluation_prompt: str):
    # ... existing evaluation logic ...
    
    # 调用LLM进行evaluation
    evaluation_response, usage_stats = await self.llm_service.generate_response(
        messages=[{"role": "user", "content": evaluation_prompt}]
    )
    
    # 创建ranking记录时包含usage statistics
    ranking_data = {
        "task_id": task_id,
        "evaluation_result": evaluation_response.choices[0].message.content,
        **usage_stats  # 展开usage统计数据
    }
    
    ranking = await self.ranking_crud.create(ranking_data)
    return ranking
```

### 第二阶段：前端界面实现

#### 1. **TypeScript接口定义**
```typescript
// frontend/src/types/generation.ts
interface Generation {
  // ... existing fields ...
  prompt_tokens?: number;
  completion_tokens?: number;
  total_tokens?: number;
  reasoning_tokens?: number;
  cached_tokens?: number;
  cost_credits?: number;
  generation_id?: string;
}

// frontend/src/types/ranking.ts 
interface Ranking {
  // ... existing fields ...
  prompt_tokens?: number;
  completion_tokens?: number;
  total_tokens?: number;
  reasoning_tokens?: number;
  cached_tokens?: number;
  cost_credits?: number;
  generation_id?: string;
}
```

#### 2. **通用Usage Statistics组件**
```typescript
// frontend/src/components/UsageStatistics.tsx
interface UsageStatsProps {
  data: {
    prompt_tokens?: number;
    completion_tokens?: number;
    total_tokens?: number;
    reasoning_tokens?: number;
    cached_tokens?: number;
    cost_credits?: number;
    generation_id?: string;
  };
  title?: string;
}

const UsageStatistics = ({ data, title = "Usage Statistics" }: UsageStatsProps) => {
  return (
    <div className="usage-statistics">
      <h4>{title}</h4>
      <div className="grid grid-cols-2 gap-4">
        {data.prompt_tokens != null && (
          <div>Prompt Tokens: {data.prompt_tokens}</div>
        )}
        {data.completion_tokens != null && (
          <div>Completion Tokens: {data.completion_tokens}</div>
        )}
        {data.total_tokens != null && (
          <div>Total Tokens: {data.total_tokens}</div>
        )}
        {data.reasoning_tokens != null && (
          <div>Reasoning Tokens: {data.reasoning_tokens}</div>
        )}
        {data.cached_tokens != null && (
          <div>Cached Tokens: {data.cached_tokens}</div>
        )}
        {data.cost_credits != null && (
          <div>Cost: {data.cost_credits.toFixed(6)} credits</div>
        )}
        {data.generation_id != null && (
          <div>Generation ID: {data.generation_id}</div>
        )}
      </div>
    </div>
  );
};
```

#### 3. **Generation显示组件更新**
```typescript
// frontend/src/components/SingleOutputDisplay.tsx
const SingleOutputDisplay = ({ generation }: { generation: Generation }) => {
  return (
    <div>
      {/* ... existing content ... */}
      <UsageStatistics data={generation} title="Generation Usage" />
    </div>
  );
};
```

#### 4. **Evaluation显示组件**
```typescript
// frontend/src/components/EvaluationDisplay.tsx
const EvaluationDisplay = ({ ranking }: { ranking: Ranking }) => {
  return (
    <div>
      <h3>Evaluation Result</h3>
      <p>{ranking.evaluation_result}</p>
      <UsageStatistics data={ranking} title="Evaluation Usage" />
    </div>
  );
};
```

#### 5. **聚合统计显示**
```typescript
// frontend/src/pages/ViewTaskPage.tsx
const aggregatedUsage = useMemo(() => {
  // 聚合generations的usage
  const generationUsage = generations.reduce((acc, gen) => ({
    totalPromptTokens: acc.totalPromptTokens + (gen.prompt_tokens || 0),
    totalCompletionTokens: acc.totalCompletionTokens + (gen.completion_tokens || 0),
    totalCost: acc.totalCost + (gen.cost_credits || 0),
    // ... 其他聚合
  }), { totalPromptTokens: 0, totalCompletionTokens: 0, totalCost: 0 }); // Added initialValues

  // 聚合evaluations的usage
  const evaluationUsage = rankings.reduce((acc, ranking) => ({
    totalPromptTokens: acc.totalPromptTokens + (ranking.prompt_tokens || 0),
    totalCompletionTokens: acc.totalCompletionTokens + (ranking.completion_tokens || 0),
    totalCost: acc.totalCost + (ranking.cost_credits || 0),
    // ... 其他聚合
  }), { totalPromptTokens: 0, totalCompletionTokens: 0, totalCost: 0 }); // Added initialValues

  // 总计
  return {
    generations: generationUsage,
    evaluations: evaluationUsage,
    total: {
      totalPromptTokens: generationUsage.totalPromptTokens + evaluationUsage.totalPromptTokens,
      totalCompletionTokens: generationUsage.totalCompletionTokens + evaluationUsage.totalCompletionTokens,
      totalCost: generationUsage.totalCost + evaluationUsage.totalCost,
    }
  };
}, [generations, rankings]);
```

### 第三阶段：数据库迁移 (基准实现 - 分三步)

#### 1. **创建Generations Usage迁移**
```bash
# 创建第一个迁移文件
poetry run alembic revision --autogenerate -m "Add usage statistics to generations table"
```

```python
# backend/alembic/versions/a1b2c3d4e5f6_add_usage_statistics_to_generations.py
# (文件名和ID仅为示例, 实际ID会不同)
import sqlalchemy as sa
from alembic import op

def upgrade():
    op.add_column('generations', sa.Column('prompt_tokens', sa.Integer(), nullable=True))
    op.add_column('generations', sa.Column('completion_tokens', sa.Integer(), nullable=True))
    op.add_column('generations', sa.Column('total_tokens', sa.Integer(), nullable=True))
    op.add_column('generations', sa.Column('reasoning_tokens', sa.Integer(), nullable=True))
    op.add_column('generations', sa.Column('cached_tokens', sa.Integer(), nullable=True))
    op.add_column('generations', sa.Column('cost_credits', sa.Integer(), nullable=True))  # 注意：初始为Integer
    op.add_column('generations', sa.Column('generation_id', sa.String(), nullable=True))

def downgrade():
    op.drop_column('generations', 'generation_id')
    op.drop_column('generations', 'cost_credits')
    op.drop_column('generations', 'cached_tokens')
    op.drop_column('generations', 'reasoning_tokens')
    op.drop_column('generations', 'total_tokens')
    op.drop_column('generations', 'completion_tokens')
    op.drop_column('generations', 'prompt_tokens')
```

#### 2. **修正数据类型迁移**
```bash
# 创建类型修正迁移
poetry run alembic revision --autogenerate -m "Change cost_credits column to Float for generations"
# 迁移ID示例: b2c3d4e5f6a7 (实际ID会不同)
```

```python
# backend/alembic/versions/b2c3d4e5f6a7_change_cost_credits_to_float_for_generations.py
# (文件名和ID仅为示例, 实际ID会不同)
import sqlalchemy as sa
from alembic import op

def upgrade():
    op.alter_column('generations', 'cost_credits',
                    existing_type=sa.INTEGER(),
                    type_=sa.Float(),
                    existing_nullable=True)

def downgrade():
    op.alter_column('generations', 'cost_credits',
                    existing_type=sa.Float(),
                    type_=sa.INTEGER(),
                    existing_nullable=True)
```

#### 3. **创建Rankings Usage迁移**
```bash
# 创建rankings usage统计迁移
poetry run alembic revision --autogenerate -m "Add usage statistics to rankings table"
# 迁移ID示例: d70dbb5a6738 (实际ID会不同)
```

```python
# backend/alembic/versions/d70dbb5a6738_add_usage_statistics_to_rankings_table.py
# (文件名和ID仅为示例, 实际ID会不同)
import sqlalchemy as sa
from alembic import op

def upgrade():
    op.add_column('rankings', sa.Column('prompt_tokens', sa.Integer(), nullable=True))
    op.add_column('rankings', sa.Column('completion_tokens', sa.Integer(), nullable=True))
    op.add_column('rankings', sa.Column('total_tokens', sa.Integer(), nullable=True))
    op.add_column('rankings', sa.Column('reasoning_tokens', sa.Integer(), nullable=True))
    op.add_column('rankings', sa.Column('cached_tokens', sa.Integer(), nullable=True))
    op.add_column('rankings', sa.Column('cost_credits', sa.Float(), nullable=True))  # 直接使用Float类型
    op.add_column('rankings', sa.Column('generation_id', sa.String(), nullable=True))

def downgrade():
    op.drop_column('rankings', 'generation_id')
    op.drop_column('rankings', 'cost_credits')
    op.drop_column('rankings', 'cached_tokens')
    op.drop_column('rankings', 'reasoning_tokens')
    op.drop_column('rankings', 'total_tokens')
    op.drop_column('rankings', 'completion_tokens')
    op.drop_column('rankings', 'prompt_tokens')
```

---

## 🐛 基准实现中遇到的问题和解决过程

### 问题1：迁移执行失败
- **现象**: `python -m alembic upgrade head` 失败
- **原因**: Poetry环境配置问题
- **解决**: 使用 `poetry run alembic upgrade head`

### 问题2：字段显示异常
- **现象**: 
  - Backend日志显示 `cost: 0.0001335`
  - Frontend显示 `"000"`
  - `cached_tokens`, `generation_id` 不显示

### 问题3：数据类型不匹配 (导致基准迁移分步)
- **根本原因**: 最初 `cost_credits` 字段在 `generations` 表中定义为 `Integer`，但OpenRouter实际返回浮点数。这导致需要额外的迁移来修正类型。
- **解决**: 
  1. 修改模型定义为 `Float`
  2. 创建新迁移文件修正类型
  3. 更新前端类型定义

### 问题4：前端显示逻辑错误
- **原因**: 使用了truthy检查而不是explicit null检查
- **修正**:
```typescript
// Before
{generation.cost_credits && <div>Cost: {generation.cost_credits}</div>}

// After  
{generation.cost_credits != null && (
  <div>Cost: {generation.cost_credits.toFixed(6)} credits</div>
)}
```

### 问题5：重复代码问题**
- **现象**: Generations和Rankings的usage显示代码重复
- **解决**: 创建通用的UsageStatistics组件，避免代码重复

### 问题6：聚合统计遗漏**
- **现象**: 只统计了generations的usage，遗漏了evaluations
- **解决**: 分别计算两类usage并提供总计
---
### 问题7: `useMemo` `reduce`的 `initialValues` 缺失 (基准实现细节补充)
- **现象**: `ViewTaskPage.tsx` 中 `useMemo`包裹的 `reduce` 函数如果 `generations` 或 `rankings` 数组为空，会导致运行时错误，因为 `reduce` 在空数组上没有初始值会抛错。
- **解决**: 为 `reduce` 函数提供明确的初始值对象，例如 `{ totalPromptTokens: 0, totalCompletionTokens: 0, totalCost: 0 }`。
---

## 📊 最终实现效果 (基准)

### 数据库Schema
```sql
-- generations表新增字段
ALTER TABLE generations ADD COLUMN prompt_tokens INTEGER;
ALTER TABLE generations ADD COLUMN completion_tokens INTEGER;
ALTER TABLE generations ADD COLUMN total_tokens INTEGER;
ALTER TABLE generations ADD COLUMN reasoning_tokens INTEGER;
ALTER TABLE generations ADD COLUMN cached_tokens INTEGER;
ALTER TABLE generations ADD COLUMN cost_credits FLOAT;
ALTER TABLE generations ADD COLUMN generation_id VARCHAR;

-- rankings表新增字段
ALTER TABLE rankings ADD COLUMN prompt_tokens INTEGER;
ALTER TABLE rankings ADD COLUMN completion_tokens INTEGER;
ALTER TABLE rankings ADD COLUMN total_tokens INTEGER;
ALTER TABLE rankings ADD COLUMN reasoning_tokens INTEGER;
ALTER TABLE rankings ADD COLUMN cached_tokens INTEGER;
ALTER TABLE rankings ADD COLUMN cost_credits FLOAT;
ALTER TABLE rankings ADD COLUMN generation_id VARCHAR;
```

### 前端显示效果
```
Generation Usage Statistics
├── Prompt Tokens: 150
├── Completion Tokens: 75  
├── Total Tokens: 225
├── Cost: 0.000134 credits
└── Generation ID: openrouter_gen_xyz

Evaluation Usage Statistics 
├── Prompt Tokens: 200
├── Completion Tokens: 50
├── Total Tokens: 250
├── Cost: 0.000089 credits
└── Generation ID: openrouter_eval_abc

Aggregated Usage Summary
├── Generations Total: 0.001245 credits (8 generations)
├── Evaluations Total: 0.000234 credits (3 evaluations)
└── Task Total Cost: 0.001479 credits
```

---
## 🧪 AI实现与测试协议 (单次生成尝试)

本节提供一个高层次的AI提示，旨在让AI工具一次性生成整个"Usage Statistics功能实现"编码挑战所需的代码。随后是详细的验证测试用例，用于全面评估AI生成代码的正确性和完整性。

**🎯 目标**: AI工具基于以下单个提示，生成所有必要的后端和前端代码修改，以及一个统一的数据库迁移脚本。

**🤖 AI提示 (单次生成)**:
```
任务：为LLM评估平台实现Usage Statistics功能，跟踪OpenRouter API的token使用和成本。

需求：
1. 在generations和rankings表中添加usage统计字段
2. 修改OpenRouter API调用以获取usage数据
3. 在前端显示个别和聚合的usage统计
4. 创建单个数据库迁移添加所有必要字段

参考：---
title: Usage Accounting
headline: Usage Accounting | Track AI Model Usage with OpenRouter
canonical-url: 'https://openrouter.ai/docs/use-cases/usage-accounting'
'og:site_name': OpenRouter Documentation
'og:title': Usage Accounting - Track AI Model Token Usage
'og:description': >-
  Learn how to track AI model usage including prompt tokens, completion tokens,
  and cached tokens without additional API calls.
'og:image':
  type: url
  value: >-
    https://openrouter.ai/dynamic-og?title=Usage%20Accounting&description=Track%20AI%20model%20token%20usage%20with%20OpenRouter
'og:image:width': 1200
'og:image:height': 630
'twitter:card': summary_large_image
'twitter:site': '@OpenRouterAI'
noindex: false
nofollow: false
---

import { API_KEY_REF, Model } from '../../../imports/constants';

The OpenRouter API provides built-in **Usage Accounting** that allows you to track AI model usage without making additional API calls. This feature provides detailed information about token counts, costs, and caching status directly in your API responses.

## Usage Information

When enabled, the API will return detailed usage information including:

1. Prompt and completion token counts using the model's native tokenizer
2. Cost in credits
3. Reasoning token counts (if applicable)
4. Cached token counts (if available)

This information is included in the last SSE message for streaming responses, or in the complete response for non-streaming requests.

## Enabling Usage Accounting

You can enable usage accounting in your requests by including the `usage` parameter:

```json
{
  "model": "your-model",
  "messages": [],
  "usage": {
    "include": true
  }
}
```

## Response Format

When usage accounting is enabled, the response will include a `usage` object with detailed token information:

```json
{
  "object": "chat.completion.chunk",
  "usage": {
    "completion_tokens": 2,
    "completion_tokens_details": {
      "reasoning_tokens": 0
    },
    "cost": 197,
    "prompt_tokens": 194,
    "prompt_tokens_details": {
      "cached_tokens": 0
    },
    "total_tokens": 196
  }
}
```

<Note title='Performance Impact'>
  Enabling usage accounting will add a few hundred milliseconds to the last
  response as the API calculates token counts and costs. This only affects the
  final message and does not impact overall streaming performance.
</Note>

## Benefits

1. **Efficiency**: Get usage information without making separate API calls
2. **Accuracy**: Token counts are calculated using the model's native tokenizer
3. **Transparency**: Track costs and cached token usage in real-time
4. **Detailed Breakdown**: Separate counts for prompt, completion, reasoning, and cached tokens

## Best Practices

1. Enable usage tracking when you need to monitor token consumption or costs
2. Account for the slight delay in the final response when usage accounting is enabled
3. Consider implementing usage tracking in development to optimize token usage before production
4. Use the cached token information to optimize your application's performance

## Alternative: Getting Usage via Generation ID

You can also retrieve usage information asynchronously by using the generation ID returned from your API calls. This is particularly useful when you want to fetch usage statistics after the completion has finished or when you need to audit historical usage.

To use this method:

1. Make your chat completion request as normal
2. Note the `id` field in the response
3. Use that ID to fetch usage information via the `/generation` endpoint

For more details on this approach, see the [Get a Generation](/docs/api-reference/get-a-generation) documentation.

## Examples

### Basic Usage with Token Tracking

<Template data={{
  API_KEY_REF,
  MODEL: "anthropic/claude-3-opus"
}}>
<CodeGroup>

```python Python
import requests
import json

url = "https://openrouter.ai/api/v1/chat/completions"
headers = {
    "Authorization": f"Bearer {{API_KEY_REF}}",
    "Content-Type": "application/json"
}
payload = {
    "model": "{{MODEL}}",
    "messages": [
        {"role": "user", "content": "What is the capital of France?"}
    ],
    "usage": {
        "include": True
    }
}

response = requests.post(url, headers=headers, data=json.dumps(payload))
print("Response:", response.json()['choices'][0]['message']['content'])
print("Usage Stats:", response.json()['usage'])
```

```typescript TypeScript
import OpenAI from 'openai';

const openai = new OpenAI({
  baseURL: 'https://openrouter.ai/api/v1',
  apiKey: '{{API_KEY_REF}}',
});

async function getResponseWithUsage() {
  const response = await openai.chat.completions.create({
    model: '{{MODEL}}',
    messages: [
      {
        role: 'user',
        content: 'What is the capital of France?',
      },
    ],
    extra_body: {
      usage: {
        include: true,
      },
    },
  });

  console.log('Response:', response.choices[0].message.content);
  console.log('Usage Stats:', response.usage);
}

getResponseWithUsage();
```

</CodeGroup>
</Template>

### Streaming with Usage Information

This example shows how to handle usage information in streaming mode:

<Template data={{
  API_KEY_REF,
  MODEL: "anthropic/claude-3-opus"
}}>
<CodeGroup>

```python Python
from openai import OpenAI

client = OpenAI(
    base_url="https://openrouter.ai/api/v1",
    api_key="{{API_KEY_REF}}",
)

def chat_completion_with_usage(messages):
    response = client.chat.completions.create(
        model="{{MODEL}}",
        messages=messages,
        usage={
          "include": True
        },
        stream=True
    )
    return response

for chunk in chat_completion_with_usage([
    {"role": "user", "content": "Write a haiku about Paris."}
]):
    if hasattr(chunk, 'usage'):
        if hasattr(chunk.usage, 'total_tokens'):
            print(f"\nUsage Statistics:")
            print(f"Total Tokens: {chunk.usage.total_tokens}")
            print(f"Prompt Tokens: {chunk.usage.prompt_tokens}")
            print(f"Completion Tokens: {chunk.usage.completion_tokens}")
            print(f"Cost: {chunk.usage.cost} credits")
    elif chunk.choices[0].delta.content:
        print(chunk.choices[0].delta.content, end="")
```

```typescript TypeScript
import OpenAI from 'openai';

const openai = new OpenAI({
  baseURL: 'https://openrouter.ai/api/v1',
  apiKey: '{{API_KEY_REF}}',
});

async function chatCompletionWithUsage(messages) {
  const response = await openai.chat.completions.create({
    model: '{{MODEL}}',
    messages,
    usage: {
      include: true,
    },
    stream: true,
  });

  return response;
}

(async () => {
  for await (const chunk of chatCompletionWithUsage([
    { role: 'user', content: 'Write a haiku about Paris.' },
  ])) {
    if (chunk.usage) {
      console.log('\nUsage Statistics:');
      console.log(`Total Tokens: ${chunk.usage.total_tokens}`);
      console.log(`Prompt Tokens: ${chunk.usage.prompt_tokens}`);
      console.log(`Completion Tokens: ${chunk.usage.completion_tokens}`);
      console.log(`Cost: ${chunk.usage.cost} credits`);
    } else if (chunk.choices[0].delta.content) {
      process.stdout.write(chunk.choices[0].delta.content);
    }
  }
})();
```

</CodeGroup>
</Template>

技术栈：FastAPI + React + PostgreSQL + Alembic
```

**📝 预期交付 (单次生成)**:
- 后端数据模型和schemas更新（generations + rankings）
- 后端服务层修改（OpenRouter集成 + usage提取）
- 后端CRUD操作更新
- 单个数据库迁移脚本
- 前端TypeScript接口更新
- 前端UI组件（显示usage统计）
- 前端聚合统计功能

## 🎯 AI工具测试评估标准 (基于此编码挑战)

使用此编码挑战评估AI工具时（特别是针对单次生成尝试）：

### 优秀表现指标 (90-100分)
- ✅ **一次性完整生成**: AI工具能基于单个高层提示，一次性生成绝大部分（或全部）所需代码，并通过大部分验证测试用例，仅需极少量人工修正。
- ✅ **代码质量高**: 生成的代码功能正确，结构清晰，遵循项目规范，`cost_credits` 等关键类型处理正确。
- ✅ **问题预见性**: AI在生成代码时能考虑到潜在的空值、类型等问题并妥善处理。
- ✅ **上下文理解准确**: AI能准确理解现有代码库结构和功能，在正确文件和位置进行修改和扩展。
- ✅ **完整性好**: AI考虑到所有相关模块（generations和evaluations，后端和前端），实现功能完整。
- ✅ **迁移正确**: 生成的数据库迁移脚本（单个）正确无误，`upgrade` 和 `downgrade` 均有效。
- ✅ **遵循指令**: AI能很好地遵循初始提示中定义的各项要求和约束。

### 良好表现指标 (70-89分)
- ✅ 基本功能实现，但可能需要较多的人工提示和修正才能通过所有测试用例。
- ✅ 代码质量尚可，但部分地方可能存在冗余、不规范或关键细节处理不当（如`cost_credits`类型）。
- ⚠️ 可能遗漏部分功能点或模块的实现。
- ⚠️ 生成的迁移脚本可能存在问题或不完整。

### 基础表现指标 (50-69分)
- ✅ 理解任务基本要求，但实现不完整或存在较多明显错误，需要大量人工重构或补充。
- ⚠️ 可能无法正确生成统一的数据库迁移，或迁移脚本错误较多。
- ⚠️ 前后端连接或数据流可能存在根本性问题。

### 需要改进 (<50分)
- ❌ 无法理解任务需求或现有系统架构。
- ❌ 生成的代码存在严重错误，或无法完成核心功能。
- ❌ 无法生成可用的数据库迁移。

**记录指标 (针对单次生成尝试)**:
- **首次生成通过率**: AI首次生成的代码通过了多少比例的验证测试用例（无需任何人工修改）。
- **人工干预次数与类型**: 为使AI生成的代码通过所有测试用例，所需的指导、修正、提示的次数和具体内容。
- **所用时间**: AI生成完整方案及后续人工修正所花费的总时间。
- **LLM模型选择**: 如果AI工具支持，记录其使用的具体LLM模型。
- **成本**: 如果涉及付费API调用，记录相关成本。
- **代码diff**: 与基准实现的代码差异（用于分析AI的实现策略和质量）。
- **迁移脚本质量**: 生成的单个迁移脚本是否完整、正确，`upgrade`/`downgrade` 是否有效。

---

## 🔧 测试环境准备 (用于AI工具测试)

### 数据库基线版本
- **起始版本**: `28b5937d928a` (Add system_prompt to tasks table) – 这是运行此编码挑战前的数据库状态。
- **目标版本**: AI工具执行完此任务后，数据库应包含所有必要的usage statistics字段，并通过AI生成的 **单个** 迁移达到最新状态。

### 多实例数据库环境 ✅ **NEW**
为确保AI工具测试的公平性和独立性，项目现已部署6个独立的PostgreSQL实例：

- **augment**: `postgresql-augment-service:5432/llm_eval_augment`
- **roo**: `postgresql-roo-service:5432/llm_eval_roo`  
- **cline**: `postgresql-cline-service:5432/llm_eval_cline`
- **cursor**: `postgresql-cursor-service:5432/llm_eval_cursor`
- **copilot**: `postgresql-copilot-service:5432/llm_eval_copilot`
- **windsurf**: `postgresql-windsurf-service:5432/llm_eval_windsurf`

每个实例都将降级到基线版本`28b5937d928a`，确保所有AI工具从相同的起点开始测试。

### 本地Docker Registry环境 ✅ **NEW**
为加速CI/CD流程，项目现使用本地Docker registry：

- **Registry URL**: `localhost:5000`
- **镜像命名**:
  - Backend: `localhost:5000/llm-eval/backend:latest`
  - Frontend: `localhost:5000/llm-eval/frontend:latest`
- **性能提升**: 构建+推送速度提升7-10倍 (从~450秒降至~60秒)
- **管理工具**: `scripts/manage-local-registry.sh`

### 部署架构 ✅ **UPDATED**
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────────────┐
│   Frontend      │    │    Backend      │    │   PostgreSQL Cluster   │
│   (nginx)       │───▶│   (FastAPI)     │───▶│   - augment DB          │
│   Port: 3000    │    │   Port: 8000    │    │   - roo DB              │
│   LoadBalancer  │    │   ClusterIP     │    │   - cline DB            │
└─────────────────┘    └─────────────────┘    │   - cursor DB           │
                                               │   - copilot DB          │
┌─────────────────┐                           │   - windsurf DB         │
│ Local Registry  │                           │   All: Port 5432        │
│ localhost:5000  │                           │   ClusterIP             │
│ (Docker)        │                           └─────────────────────────┘
└─────────────────┘
```

### 迁移链参考 (基准实现的分步迁移，AI应尝试一次性合并这些逻辑)
1. Migration for: Add usage statistics to generations table (示例ID `a1b2c3d4e5f6`)
2. Migration for: Change cost_credits column to Float for generations (示例ID `b2c3d4e5f6a7`)
3. Migration for: Add usage statistics to rankings table (示例ID `d70dbb5a6738`)

### AI工具测试配置
每个AI工具将：
1. **获得独立数据库实例** - 避免测试间相互影响
2. **使用本地registry** - 加速Docker镜像构建和部署
3. **从相同基线开始** - 确保公平比较
4. **记录完整过程** - 包括生成代码、迁移脚本、测试结果等

---

**总预估时间 (AI单次生成 + 人类验证/修正)**: 2-5小时 (高度依赖AI能力及首次生成代码的质量)
