# CI/CD and Kubernetes Deployment Guide

This document provides a comprehensive guide for the CI/CD pipeline and Kubernetes deployment setup for the LLM Evaluation Platform.

## Overview

The project uses a modern CI/CD pipeline with the following components:

- **CI/CD Platform**: GitHub Actions
- **Container Registry**: GitHub Container Registry (ghcr.io)
- **Orchestration**: Kubernetes (k3s for local development)
- **Database**: PostgreSQL
- **Backend**: FastAPI (Python)
- **Frontend**: React (TypeScript) with Nginx

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Developer     │    │  GitHub Actions │    │   k3s Cluster   │
│                 │    │                 │    │                 │
│ git push main   │───▶│  1. CI Tests    │───▶│  Auto Deploy    │
│                 │    │  2. Build Images│    │                 │
│                 │    │  3. Push to GHCR│    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## CI/CD Pipeline

### Workflow Triggers

The pipeline is triggered on:
- Push to `main` or `develop` branches
- Pull requests to `main` branch

### Pipeline Stages

#### 1. Backend CI (`backend-ci`)
- **Environment**: Ubuntu Latest
- **Python Version**: 3.12
- **Steps**:
  - Checkout code
  - Set up Python and Poetry
  - Cache dependencies
  - Install dependencies
  - Run linting (Flake8)
  - Run tests (Pytest)

#### 2. Frontend CI (`frontend-ci`)
- **Environment**: Ubuntu Latest
- **Node.js Version**: 20
- **Steps**:
  - Checkout code
  - Set up Node.js
  - Install dependencies
  - Run linting (ESLint)
  - Build application

#### 3. Build and Push (`build-and-push`)
- **Triggers**: Only on push to `main` branch
- **Dependencies**: Requires both CI jobs to pass
- **Steps**:
  - Build Docker images for backend and frontend
  - Push images to GitHub Container Registry
  - Tag images with branch name, SHA, and `latest`

#### 4. Deploy (`deploy`)
- **Triggers**: Only on push to `main` branch after successful build
- **Dependencies**: Requires build-and-push to complete
- **Steps**:
  - Configure kubectl for k3s cluster
  - Update image tags in Kubernetes manifests
  - Deploy to k3s cluster
  - Verify deployment status

## Local Development Setup

### Prerequisites

1. **k3s**: Lightweight Kubernetes distribution
   ```bash
   curl -sfL https://get.k3s.io | sh -
   ```

2. **kubectl**: Kubernetes CLI
   ```bash
   # Usually installed with k3s, or install separately
   curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
   ```

3. **Docker**: Container runtime
   ```bash
   # Install Docker according to your OS
   ```

### Local Deployment

1. **Quick Deploy**:
   ```bash
   ./scripts/deploy-local.sh
   ```

2. **Manual Steps**:
   ```bash
   # Build images
   docker build -t llm-eval-backend:latest ./backend
   docker build -t llm-eval-frontend:latest ./frontend
   
   # Import to k3s
   docker save llm-eval-backend:latest | sudo k3s ctr images import -
   docker save llm-eval-frontend:latest | sudo k3s ctr images import -
   
   # Deploy
   kubectl apply -f k8s/namespace.yaml
   kubectl apply -f k8s/postgresql.yaml
   kubectl apply -f k8s/backend.yaml
   kubectl apply -f k8s/frontend.yaml
   ```

3. **Access Application**:
   ```bash
   # Check services
   kubectl get services -n llm-eval
   
   # Port forward if needed
   kubectl port-forward service/frontend-service 3000:3000 -n llm-eval
   kubectl port-forward service/backend-service 8000:8000 -n llm-eval
   ```

4. **Cleanup**:
   ```bash
   ./scripts/cleanup.sh
   ```

## Production Deployment

### GitHub Secrets Configuration

For the CI/CD pipeline to work, configure these secrets in your GitHub repository:

1. **KUBECONFIG**: Base64 encoded kubeconfig file for your k3s cluster
   ```bash
   # Generate the secret
   cat ~/.kube/config | base64 -w 0
   ```

2. **OPENROUTER_API_KEY**: Your OpenRouter API key (update in k8s/backend.yaml)

### Setting up Remote k3s

1. **Install k3s on your server**:
   ```bash
   curl -sfL https://get.k3s.io | sh -
   ```

2. **Get kubeconfig**:
   ```bash
   sudo cat /etc/rancher/k3s/k3s.yaml
   ```

3. **Update server URL** in kubeconfig to your server's IP

4. **Add to GitHub Secrets** as base64 encoded string

### Environment Configuration

Update the following files for production:

1. **k8s/backend.yaml**: Update the `OPENROUTER_API_KEY` secret
2. **k8s/postgresql.yaml**: Update database credentials
3. **frontend/nginx.conf**: Update backend service URL if needed

## Kubernetes Resources

### Namespace
- **Name**: `llm-eval`
- **Purpose**: Isolate application resources

### PostgreSQL
- **Image**: `postgres:15-alpine`
- **Storage**: 10Gi PVC with `local-path` storage class
- **Credentials**: Stored in Kubernetes secrets

### Backend (FastAPI)
- **Image**: Built from `backend/Dockerfile`
- **Replicas**: 2
- **Resources**: 512Mi memory, 250m CPU (requests)
- **Health Checks**: HTTP probe on `/api/v1/health` endpoint
- **Environment**: Database connection via ConfigMap and Secrets

### Frontend (React + Nginx)
- **Image**: Built from `frontend/Dockerfile`
- **Replicas**: 2
- **Resources**: 128Mi memory, 100m CPU (requests)
- **Health Checks**: HTTP probe on `/health` endpoint
- **Service**: LoadBalancer type for external access

## Monitoring and Troubleshooting

### Useful Commands

```bash
# Check pod status
kubectl get pods -n llm-eval

# Check services
kubectl get services -n llm-eval

# View logs
kubectl logs -l app=backend -n llm-eval
kubectl logs -l app=frontend -n llm-eval
kubectl logs -l app=postgresql -n llm-eval

# Describe resources
kubectl describe pod <pod-name> -n llm-eval

# Port forwarding for debugging
kubectl port-forward service/backend-service 8000:8000 -n llm-eval
kubectl port-forward service/frontend-service 3000:3000 -n llm-eval

# Execute into pods
kubectl exec -it <pod-name> -n llm-eval -- /bin/bash
```

### Common Issues

1. **Images not found**: Ensure images are built and imported to k3s
2. **Database connection issues**: Check PostgreSQL pod status and credentials
3. **Service not accessible**: Verify service type and port configuration
4. **Pod crashes**: Check logs and resource limits

### Health Checks

- **Backend**: `GET /api/v1/health` returns detailed system health information including database connectivity
- **Frontend**: `GET /health` returns `healthy` text response
- **PostgreSQL**: `pg_isready` command

## Security Considerations

1. **Non-root containers**: All containers run as non-root users
2. **Resource limits**: CPU and memory limits set for all containers
3. **Secrets management**: Sensitive data stored in Kubernetes secrets
4. **Network policies**: Consider implementing network policies for production
5. **Image scanning**: Consider adding image vulnerability scanning to CI pipeline

## Scaling

### Horizontal Scaling
```bash
# Scale backend
kubectl scale deployment backend --replicas=5 -n llm-eval

# Scale frontend
kubectl scale deployment frontend --replicas=3 -n llm-eval
```

### Vertical Scaling
Update resource requests/limits in deployment manifests and apply:
```bash
kubectl apply -f k8s/backend.yaml
kubectl apply -f k8s/frontend.yaml
```

## Backup and Recovery

### Database Backup
```bash
# Create backup
kubectl exec -it <postgresql-pod> -n llm-eval -- pg_dump -U postgres llm_eval > backup.sql

# Restore backup
kubectl exec -i <postgresql-pod> -n llm-eval -- psql -U postgres llm_eval < backup.sql
```

### Persistent Volume Backup
Backup the PVC data according to your storage provider's recommendations.

## Future Enhancements

1. **Helm Charts**: Package application as Helm chart for easier deployment
2. **GitOps**: Implement ArgoCD or Flux for GitOps deployment
3. **Monitoring**: Add Prometheus and Grafana for monitoring
4. **Logging**: Implement centralized logging with ELK stack
5. **Service Mesh**: Consider Istio for advanced traffic management
6. **Auto-scaling**: Implement HPA (Horizontal Pod Autoscaler)
7. **Multi-environment**: Set up staging and production environments 