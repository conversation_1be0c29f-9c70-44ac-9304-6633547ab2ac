# Backend CI/CD Implementation Plan

## 1. Introduction

This document outlines the plan to implement Continuous Integration (CI) and Continuous Delivery (CD) for the backend of the LLM Evaluation Platform. The goal is to automate testing, linting, and potentially building and deploying the backend application to ensure code quality, stability, and faster release cycles.

## 2. Goals

-   **Automated Testing:** Automatically run unit and integration tests on every push and pull request to the main branches.
-   **Code Quality Checks:** Enforce code style and quality using linters.
-   **Build Automation:** (Optional, Future) Automate the process of building a distributable artifact (e.g., Docker container).
-   **Deployment Automation:** (Optional, Future) Automate deployment to staging and production environments.
-   **Faster Feedback Loop:** Provide developers with quick feedback on their changes.
-   **Improved Reliability:** Reduce the risk of introducing regressions or bugs into the codebase.

## 3. CI/CD Tool Selection

-   **GitHub Actions:** We will use GitHub Actions as the CI/CD platform. It integrates well with GitHub repositories and offers a generous free tier for public and private projects.

## 4. CI Pipeline Stages (Initial Focus)

The initial CI pipeline will focus on ensuring code quality and correctness. It will be triggered on pushes to the `main` branch and on pull requests targeting `main`.

The pipeline will consist of the following stages/jobs:

1.  **Checkout Code:**
    -   Action: `actions/checkout@v3` (or newer).
    -   Purpose: Checks out the repository code.

2.  **Set up Python Environment:**
    -   Action: `actions/setup-python@v4` (or newer).
    -   Purpose: Installs a specific Python version.
    -   Configuration: Specify Python version (e.g., 3.9, 3.10, 3.11, matching project requirements).

3.  **Install Poetry:**
    -   Purpose: Poetry is used for dependency management.
    -   Commands:
        ```bash
        pip install poetry
        ```

4.  **Install Dependencies:**
    -   Purpose: Install project dependencies using Poetry.
    -   Commands:
        ```bash
        cd backend # Ensure this is run from the root of the repo, or adjust path
        poetry install --no-root --no-dev # For CI, only install runtime dependencies initially
        # For testing, dev dependencies might be needed: poetry install --no-root
        ```
    -   Caching: Implement caching for Poetry dependencies to speed up builds (`actions/cache`).

5.  **Linting:**
    -   Purpose: Check code style and quality.
    -   Tool: Flake8 (or Pylint, based on project standards).
    -   Commands:
        ```bash
        cd backend
        poetry run flake8 . # Or pylint <module_names>
        ```

6.  **Testing:**
    -   Purpose: Run automated tests.
    -   Tool: Pytest.
    -   Commands:
        ```bash
        cd backend
        poetry run pytest
        ```
    -   Environment: May require setting up a test database or environment variables (e.g., a dummy `OPENROUTER_API_KEY` if tests make external calls, though mocking is preferred).

## 5. Workflow File

-   **Location:** `.github/workflows/backend-ci.yml`
-   This YAML file will define the triggers, jobs, and steps for the CI pipeline.

## 6. Branching Strategy & Triggers

-   **Triggers:**
    -   On push to the `main` branch.
    -   On `pull_request` events targeting the `main` branch.

## 7. Secrets Management

-   **`OPENROUTER_API_KEY`:** If tests require a live API key (which should be avoided by mocking), this will be stored as a GitHub Actions secret and passed to the workflow as an environment variable. For linting and most unit tests, this should not be necessary.

## 8. Future CD Pipeline Stages (Conceptual)

Once CI is stable, the following CD stages can be considered:

1.  **Build Docker Image:**
    -   Purpose: Package the application into a Docker container.
    -   Action: Use Docker build actions (e.g., `docker/build-push-action@v4`).
    -   Requires: A `Dockerfile` in the `backend/` directory.

2.  **Push to Container Registry:**
    -   Purpose: Store the Docker image in a registry (e.g., GitHub Container Registry, Docker Hub, AWS ECR).

3.  **Deploy to Staging:**
    -   Purpose: Deploy the application to a staging environment for further testing.
    -   Method: Depends on the hosting environment (e.g., SSH, kubectl apply, cloud-specific deployment actions).

4.  **Deploy to Production:**
    -   Purpose: Deploy the application to the production environment.
    -   Method: Similar to staging, often with manual approval steps.

## 9. Implementation Steps

1.  Create the `.github/workflows/` directory in the project root.
2.  Create the `backend-ci.yml` file with the defined CI pipeline stages for linting and testing.
3.  Configure Python version and Poetry setup.
4.  Add Flake8 and Pytest to `pyproject.toml` if not already present as dev dependencies.
5.  Ensure tests can run in a CI environment (e.g., handle database setup/teardown if needed, mock external services).
6.  Commit the workflow file and push to trigger the action.
7.  Iterate on the workflow file to fix any issues and ensure all steps pass.
8.  (Future) Develop a `Dockerfile` for the backend.
9.  (Future) Extend the `backend-ci.yml` to include Docker build and push stages.
10. (Future) Plan and implement deployment stages for staging/production environments.

This plan focuses on establishing a solid CI foundation first. 