# LLM Evaluation Platform: User Guide

This guide explains how to use the LLM Evaluation Platform to generate text from multiple Large Language Models (LLMs) and evaluate their outputs.

## 1. Accessing the Platform

Open the provided URL for the LLM Evaluation Platform in your web browser. The main interface consists of a sidebar for task history and a main content area.

## 2. Starting a New Evaluation (New Conversation)

1.  **New Conversation:**
    *   If you are on the main page, you might already be in a "New Conversation" view.
    *   Alternatively, click the "New Conversation" button (often a plus icon) in the history sidebar to clear the current view and start fresh.

2.  **Enter Prompt:**
    *   In the main content area, you will see a text input field. Type or paste the prompt you want the LLMs to respond to.

3.  **Select Generator Models:**
    *   Below the prompt input, you will find a section to select "Generator Models."
    *   Choose one or more LLMs from the list of available models. These models will generate responses to your prompt.
    *   You must select at least one generator model.

4.  **Generate Outputs:**
    *   Once your prompt is entered and models are selected, click the "Generate Outputs" button.
    *   The platform will now send your prompt to the selected generator models.

## 3. Monitoring Generation

-   **Real-time Updates:** As the models generate responses, their outputs will stream into the "Generated Outputs" section of the page.
-   **Model Tabs/Selector:** You can typically switch between the outputs of different models using tabs or a dropdown selector if multiple models were chosen for generation.
-   **Status Indicators:** The overall task status (e.g., GENERATING, COMPLETED, FAILED) will be displayed.
-   **Reasoning (Optional):** If a model provides reasoning for its generation process, this might be displayed alongside or within its output, often in a collapsible section.

## 4. Evaluating Generated Outputs

Once generation is complete (or even if some generations failed, as long as there are at least two successful outputs), you can proceed to evaluation.

1.  **Select Evaluator Models:**
    *   An "Evaluation Report" or "Evaluate Area" section will become available.
    *   Here, select one or more "Evaluator Models." These LLMs will act as judges to score the outputs from the generator models.
    *   You must select at least one evaluator model.

2.  **Generate Evaluation Report:**
    *   Click the "Generate Evaluation Report" (or similar) button.
    *   The platform will send the generated outputs and the original prompt to the selected evaluator models.

## 5. Viewing the Evaluation Report

-   **Report Display:** Once the evaluators have finished, an "Evaluation Results" or "Report Viewer" section will display the findings.
-   **Evaluator Tabs/Selector:** If multiple evaluators were used, you might be able to switch between their individual reports using a dropdown or tabs.
-   **Ranking:** Each evaluator's report will typically show a ranked list of the generator model outputs, from best to worst, based on its assessment.
-   **Reasoning:** Crucially, the report will include the reasoning provided by the evaluator LLM for its ranking decisions.
-   **Error Messages:** If an evaluator failed to produce a ranking, an error message will be displayed for that evaluator.

## 6. Managing Tasks (History)

-   **History Sidebar:** The sidebar on the left displays a list of your past tasks (conversations).
-   **Task Information:** Each item in the history usually shows a snippet of the prompt, the task status, and the creation date/time.
-   **Selecting a Task:** Click on a task in the history to load its details, including the original prompt, all generated outputs, and any evaluation reports associated with it.
-   **Searching History:** A search bar is typically available to filter the task history based on prompt content.
-   **Deleting a Task:** You can usually delete tasks from the history. A confirmation prompt will appear before deletion.

## 7. Dark/Light Mode

-   The platform typically supports both dark and light themes. Look for a toggle button (often a sun/moon icon) in the header to switch between modes.

## 8. Interpreting Results

-   **Compare Rankings:** See if different evaluators agree on the best outputs.
-   **Analyze Reasoning:** The reasoning provided by evaluators is key to understanding *why* certain outputs were preferred. This can offer deeper insights than just the ranking itself.
-   **Iterate:** Use the evaluation results to refine your prompts, try different models, or make decisions based on LLM performance for your specific use case. 