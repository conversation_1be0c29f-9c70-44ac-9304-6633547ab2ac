# PostgreSQL and Logging Setup Guide for LLM Eval Platform

This document outlines the steps to set up PostgreSQL as the backend database and configure a robust logging system for the LLM Eval Platform.

## Part 1: PostgreSQL Setup

This section covers installing PostgreSQL, creating the necessary database and user, and configuring the application and Alembic to use PostgreSQL.

### 1.1. Installation (Ubuntu/WSL)

1.  **Update package lists:**
    ```bash
    sudo apt update
    ```
2.  **Install PostgreSQL and its contrib package:**
    ```bash
    sudo apt install postgresql postgresql-contrib
    ```
3.  **Verify installation:**
    ```bash
    psql --version
    ```

### 1.2. Starting and Enabling PostgreSQL Service

1.  **Start the PostgreSQL service:**
    ```bash
    sudo service postgresql start
    ```
2.  **Check the service status:**
    ```bash
    sudo service postgresql status
    ```
3.  **Enable the service to start on boot (if applicable):**
    ```bash
    sudo systemctl enable postgresql
    ```
    *(Note: WSL might manage services differently; `service postgresql start` might be sufficient for sessions).*

### 1.3. Initial PostgreSQL Configuration

1.  **Access the PostgreSQL prompt as the `postgres` superuser:**
    ```bash
    sudo -u postgres psql
    ```
2.  **Set a password for the `postgres` user (recommended):**
    ```sql
    \password postgres
    ```
    (Enter your desired strong password when prompted)
3.  **Create a dedicated database user for the application:**
    ```sql
    CREATE USER llm_eval_platform_user WITH PASSWORD 'your_chosen_strong_password';
    ```
    *(Replace `'your_chosen_strong_password'` with a secure password)*
4.  **Create the application database, owned by the new user:**
    ```sql
    CREATE DATABASE llm_eval_platform_db OWNER llm_eval_platform_user;
    ```
5.  **Grant all privileges on this database to the new user:**
    ```sql
    GRANT ALL PRIVILEGES ON DATABASE llm_eval_platform_db TO llm_eval_platform_user;
    ```
6.  **Exit the `psql` prompt:**
    ```sql
    \q
    ```

### 1.4. Updating Application Configuration

1.  **Modify your `.env` file** in the `llm-eval-platform/backend/` directory to point to the new PostgreSQL database.
    Update or create the `DATABASE_URL` variable:
    ```env
    DATABASE_URL=postgresql+asyncpg://llm_eval_platform_user:your_chosen_strong_password@localhost:5432/llm_eval_platform_db
    ```
    *   Replace `llm_eval_platform_user` and `your_chosen_strong_password` with the credentials you created.
    *   `asyncpg` is the asynchronous driver for PostgreSQL.
    *   `localhost:5432` is the default host and port for PostgreSQL.

### 1.5. Python Dependencies

1.  **Update `pyproject.toml`** in `llm-eval-platform/backend/`:
    *   Add `asyncpg` to your dependencies:
        ```toml
        asyncpg = "^0.29.0" # Or the latest stable version
        ```
    *   Remove `aiosqlite` if it's listed.
2.  **Update your Poetry lock file and install dependencies:**
    Navigate to `llm-eval-platform/backend/` in your terminal and run:
    ```bash
    poetry lock
    poetry install
    ```

### 1.6. Alembic Configuration for PostgreSQL

To use Alembic with PostgreSQL and reset the migration history for a fresh start:

1.  **Update `alembic.ini`** in `llm-eval-platform/backend/`:
    *   Change the `sqlalchemy.url` to your PostgreSQL connection string:
        ```ini
        sqlalchemy.url = postgresql+asyncpg://llm_eval_platform_user:your_chosen_strong_password@localhost:5432/llm_eval_platform_db
        ```

2.  **Update `alembic/env.py`** in `llm-eval-platform/backend/`:
    *   Ensure `target_metadata` is imported from your application's models (e.g., `from app.db.base_class import Base` and set `target_metadata = Base.metadata`).
    *   Modify the `run_migrations_online` function to handle asynchronous connections with `asyncpg`.

    ```python
    # Inside alembic/env.py

    # ... other imports ...
    from sqlalchemy.ext.asyncio import create_async_engine # Add this
    from app.db.base_class import Base # Adjust path if your Base is elsewhere
    import asyncio # Add this

    target_metadata = Base.metadata # Make sure this points to your models' metadata

    # ...

    def run_migrations_offline() -> None:
        # ... (keep as is or adapt if needed) ...
        url = config.get_main_option("sqlalchemy.url")
        context.configure(
            url=url,
            target_metadata=target_metadata,
            literal_binds=True,
            dialect_opts={"paramstyle": "named"},
            # Add this for PostgreSQL JSONB support if not rendering default
            render_as_batch=True, 
            compare_type=True, 
        )

        with context.begin_transaction():
            context.run_migrations()


    async def run_migrations_online() -> None: # Make this function async
        """Run migrations in 'online' mode.

        In this scenario we need to create an Engine
        and associate a connection with the context.

        """
        connectable = create_async_engine( # Use create_async_engine
            config.get_main_option("sqlalchemy.url"),
            pool_pre_ping=True,
        )

        async with connectable.connect() as connection: # Use async with
            await connection.run_sync(do_run_migrations) # Use run_sync

        await connectable.dispose() # Dispose of the engine


    def do_run_migrations(connection): # This helper remains synchronous
        context.configure(
            connection=connection, 
            target_metadata=target_metadata,
            # Add this for PostgreSQL JSONB support if not rendering default
            render_as_batch=True,
            compare_type=True,
        )

        with context.begin_transaction():
            context.run_migrations()

    if context.is_offline_mode():
        run_migrations_offline()
    else:
        asyncio.run(run_migrations_online()) # Run the async function

    ```

3.  **Reset Alembic History (for a fresh start with PostgreSQL):**
    *   **Important:** This deletes existing migration files. Ensure you have a backup or that this is the desired action (e.g., when switching database types and starting fresh).
    *   Delete all files within the `llm-eval-platform/backend/alembic/versions/` directory.
    *   Delete any helper scripts in `llm-eval-platform/backend/alembic/scripts/` if they are SQLite specific.

4.  **Generate a New Initial Migration:**
    Navigate to `llm-eval-platform/backend/` and run:
    ```bash
    alembic revision -m "initial schema for postgresql"
    ```
    This will create a new migration file in `alembic/versions/` based on your current SQLAlchemy models.

5.  **Apply the Migration to the PostgreSQL Database:**
    ```bash
    alembic upgrade head
    ```
    This will create the tables in your empty PostgreSQL database.

## Part 2: Logging Configuration

This section describes how to set up a centralized logging configuration using a YAML file, which will be used by Uvicorn to manage logs for both the FastAPI application and Uvicorn itself.

### 2.1. Motivation

*   **Consistency:** Ensures uniform log levels and formats across the application and server.
*   **Flexibility:** Allows easy changes to logging behavior without modifying code.
*   **Centralization:** Manages all logging aspects in one place.
*   **Multiple Destinations:** Enables logging to console, files, or other handlers simultaneously.
*   **Control over Uvicorn Logs:** Provides fine-grained control over Uvicorn's error and access logs.

### 2.2. Creating `log_config.yaml`

Create a `log_config.yaml` file in the root of your `llm-eval-platform/backend/` directory (or another location, adjusting the Uvicorn command accordingly).

```yaml
version: 1
disable_existing_loggers: False

formatters:
  default:
    (): "uvicorn.logging.DefaultFormatter"
    fmt: "%(levelprefix)s %(asctime)s [%(name)s] %(message)s"
    datefmt: "%Y-%m-%d %H:%M:%S"
    use_colors: True
  simple:
    fmt: "%(asctime)s - %(levelname)s - %(name)s - %(message)s"
    datefmt: "%Y-%m-%d %H:%M:%S"
  access:
    (): "uvicorn.logging.AccessFormatter"
    fmt: '%(levelprefix)s %(asctime)s [%(name)s] %(client_addr)s - "%(request_line)s" %(status_code)s'
    datefmt: "%Y-%m-%d %H:%M:%S"
    use_colors: True

handlers:
  # Console Handler
  console:
    class: logging.StreamHandler
    formatter: default
    level: INFO
    stream: ext://sys.stdout # Ensures output goes to stdout

  # Application File Handler (Example)
  app_file_handler:
    class: logging.handlers.RotatingFileHandler
    formatter: simple
    level: INFO
    filename: logs/app.log # Ensure 'logs' directory exists
    maxBytes: 10485760 # 10MB
    backupCount: 3
    encoding: utf8

  # Uvicorn Access File Handler (Example, if you want separate access logs)
  access_file_handler:
    class: logging.handlers.RotatingFileHandler
    formatter: access # Use the specific access formatter
    level: INFO
    filename: logs/access.log # Ensure 'logs' directory exists
    maxBytes: 10485760 # 10MB
    backupCount: 3
    encoding: utf8

loggers:
  # Application Logger
  app: # This should match the name used in your application: logging.getLogger("app")
    handlers: [console, app_file_handler] # Log to console and file
    level: INFO
    propagate: False # Prevents logs from being passed to the root logger

  # Uvicorn Loggers
  uvicorn:
    handlers: [console] # Uvicorn's general logs
    level: INFO
    propagate: True # Can propagate to root if needed, or set specific handlers
  uvicorn.error:
    handlers: [console, app_file_handler] # Uvicorn errors to console and app_file
    level: INFO
    propagate: False
  uvicorn.access:
    handlers: [console, access_file_handler] # Uvicorn access logs to console and its own file
    level: INFO
    propagate: False

# Root logger (optional, as a fallback)
# root:
#   handlers: [console]
#   level: WARNING
```
*   **Ensure the `logs/` directory exists** in `llm-eval-platform/backend/` if you use file handlers.
*   Adjust logger names (e.g., `app`) to match what you use in `logging.getLogger("app_name")` in your Python code.

### 2.3. Application Code Adjustments

1.  **Obtain Logger Instance:**
    In your application modules (e.g., `app/core/config.py` or a dedicated `app/core/logging_utils.py`), get your logger instance:
    ```python
    import logging
    # If you named your logger 'app' in log_config.yaml
    log = logging.getLogger("app")
    ```

2.  **Remove `logging.basicConfig()`:**
    If you have `logging.basicConfig()` in your `main.py` or anywhere else it might conflict with Uvicorn's configuration, remove or comment it out. Uvicorn will handle the setup via `log_config.yaml`.

### 2.4. Running Uvicorn with Log Configuration

Start your Uvicorn server, pointing it to the `log_config.yaml` file:
```bash
cd llm-eval-platform/backend/
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000 --log-config log_config.yaml
```
*   Replace `app.main:app` with the actual path to your FastAPI application instance.
*   `--log-config log_config.yaml` tells Uvicorn to use your YAML file.

### 2.5. Benefits Realized

*   Your application's `log.info()`, `log.warning()`, etc. (using `logging.getLogger("app")`) will now be processed according to `log_config.yaml`.
*   Uvicorn's own logs (startup, errors, access requests) will also adhere to this configuration.
*   You can easily adjust log levels (e.g., from `INFO` to `DEBUG`), formats, and output destinations (e.g., add more file handlers, integrate with external logging services) by modifying only the `log_config.yaml` file, without code changes or application restarts (if Uvicorn picks up changes, or on next restart).

This concludes the setup guide for PostgreSQL and advanced logging configuration.
Remember to replace placeholder values like passwords and adjust paths/names according to your specific project structure. 