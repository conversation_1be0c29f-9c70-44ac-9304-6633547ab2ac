# Backend Configuration

This document outlines how to configure the backend server for the LLM Evaluation Platform.

## 1. Overview

Backend configuration is managed through environment variables. Pydantic's `BaseSettings` (in `app.core.config.py`) is used to load these variables, validate their types, and provide default values where applicable.

A `.env` file in the `backend/` directory is the primary way to set these environment variables for local development.

## 2. Environment Variables

Create a `.env` file in the `llm-eval-platform/backend/` directory by copying the `llm-eval-platform/.env.example` file and renaming it to `.env` inside the `backend` folder.

Modify the following variables in your `backend/.env` file as needed:

-   **`PROJECT_NAME`**
    -   Description: The name of the project, used in titles and messages.
    -   Default: `LLM Evaluation Platform Backend`
    -   Example: `PROJECT_NAME="My Custom LLM Evaluator"`

-   **`API_V1_STR`**
    -   Description: The prefix for version 1 of the API.
    -   Default: `/api/v1`
    -   Example: `API_V1_STR="/api/v1"`

-   **`DATABASE_URL`**
    -   Description: The connection string for the SQLAlchemy database.
    -   Default: `sqlite+aiosqlite:///./sql_app.db` (This creates an SQLite file named `sql_app.db` in the `backend/` directory).
    -   Example for PostgreSQL: `DATABASE_URL="postgresql+asyncpg://user:password@host:port/dbname"`
    -   Example for SQLite in a different location: `DATABASE_URL="sqlite+aiosqlite:///./data/prod.db"`

-   **`OPENROUTER_API_KEY`** (Critical)
    -   Description: Your API key for accessing LLM models through OpenRouter.ai.
    -   Default: `YOUR_OPENROUTER_API_KEY_HERE` (This **must** be replaced with a valid key).
    -   Example: `OPENROUTER_API_KEY="sk-or-v1-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"`

-   **`OPENROUTER_AVAILABLE_MODELS`** (Optional)
    -   Description: A comma-separated string of model identifiers that will be presented as available models in the UI. If not set, a default list hardcoded in `backend/app/api/endpoints/tasks.py` is used.
    -   Default: Not set (uses hardcoded list).
    -   Example: `OPENROUTER_AVAILABLE_MODELS="openai/gpt-4.1,google/gemini-2.5-pro-preview,anthropic/claude-3.7-sonnet"`

-   **`DEFAULT_GENERATION_MODELS`** (Optional)
    -   Description: A comma-separated string of model identifiers to be used as default generator models if the user does not specify any when creating a task. If not set, the application might require models to be explicitly chosen or use another fallback list.
    -   Default: Not set (application logic in `tasks.py` may require explicit selection or use `OPENROUTER_AVAILABLE_MODELS`'s first few if not provided by user).
    -   Example: `DEFAULT_GENERATION_MODELS="openai/gpt-4.1,google/gemini-2.5-pro-preview"`

## 3. Loading Configuration

The `app.core.config.Settings` class automatically loads values from environment variables that match its field names (case-insensitive). The `.env` file is explicitly loaded by `python-dotenv` at the beginning of `config.py`.

## 4. Custom SSL Certificate for OpenRouter

The backend service `llm_service.py` attempts to use a custom SSL certificate located at `/root/projects/report/SLB Issuing CA3.crt` when communicating with OpenRouter. If this certificate is not found or fails, it will attempt to use system certificates, and as a last resort, may disable SSL verification (which is insecure and not recommended for production).

To use your own certificate for OpenRouter (or other outbound HTTPS calls made via `httpx` in `llm_service.py`):
1.  Place your certificate file in a known location accessible to the backend application.
2.  Modify the `COMPANY_CERT_PATH` variable in `llm_eval-platform/backend/app/services/llm_service.py` to point to your certificate file path.

If no custom certificate is needed, ensure the system's root certificates are up-to-date for standard SSL verification to work.

## 5. CORS Configuration

Cross-Origin Resource Sharing (CORS) is configured in `app.main.py`. For development, `allow_origins` is typically set to `["*"]` to allow requests from any origin. In a production environment, this should be restricted to the specific domain(s) of your frontend application.

Example for production (conceptual, actual implementation might vary):
`settings.BACKEND_CORS_ORIGINS = ["https://your-frontend-domain.com"]`
This would require `BACKEND_CORS_ORIGINS` to be defined in `Settings` and potentially in the `.env` file. 