# Migration and Deployment Plan: LLM Eval Platform to PostgreSQL and Kubernetes

## Phase 1: Backend Database Migration to PostgreSQL

*   [X] **1.1. Update Dependencies:**
    *   [X] 1.1.1. Add `asyncpg` to `llm-eval-platform/backend/pyproject.toml`.
    *   [X] 1.1.2. Remove `aiosqlite` from `llm-eval-platform/backend/pyproject.toml`.
    *   [X] 1.1.3. Run `poetry lock` in the `llm-eval-platform/backend` directory to update `poetry.lock`.
    *   [X] 1.1.4. Run `poetry install` in the `llm-eval-platform/backend` directory to install new dependencies and remove old ones.
*   [X] **1.2. Update Database Configuration:**
    *   [X] 1.2.1. Modify `DATABASE_URL` in `llm-eval-platform/backend/app/core/config.py` (and corresponding `.env` file or environment variables) to use the `postgresql+asyncpg` scheme and point to your PostgreSQL instance.
        *   Example: `DATABASE_URL="postgresql+asyncpg://user:password@host:port/database_name"`
*   [X] **1.3. Reset and Initialize Alembic for PostgreSQL:**
    *   [X] 1.3.1. **CRITICAL: Ensure a complete backup of the SQLite database exists.** (User confirmed done)
    *   [X] 1.3.2. **CRITICAL: Ensure all relevant model changes are reflected in `llm-eval-platform/backend/app/db/models.py`.** (e.g., consider using `JSONB` for `ranked_list_json`).
    *   [X] 1.3.3. Delete all existing migration scripts from `llm-eval-platform/backend/alembic/versions/`.
    *   [X] 1.3.4. Update `sqlalchemy.url` in `llm-eval-platform/backend/alembic.ini` to the PostgreSQL `DATABASE_URL` (as per step 1.2.1).
    *   [X] 1.3.5. Generate a new initial Alembic revision based on current models: `poetry run alembic revision -m "Initial schema for PostgreSQL" --autogenerate` (run from `llm-eval-platform/backend/`).
    *   [X] 1.3.6. Apply the initial migration to the (empty) PostgreSQL database: `poetry run alembic upgrade head` (run from `llm-eval-platform/backend/`).
*   [X] **1.4. Verify SQLAlchemy Engine and `get_db`:**
    *   [X] 1.4.1. Ensure `create_async_engine` in `llm-eval-platform/backend/app/db/base.py` correctly uses the PostgreSQL URL. (Usually no change needed if `config.py` is correct).
    *   [X] 1.4.2. Test basic database connectivity and CRUD operations locally with the PostgreSQL backend.

## Phase 2: Data Migration and Cleansing

*   [X] **2.1. Plan Data Migration Strategy:**
    *   [X] 2.1.1. Decide on a method to transfer data from the SQLite database to the new PostgreSQL database (e.g., using `pgloader`, custom Python scripts, ETL tools). *This step is outside the direct scope of what I can execute but is crucial.*
    *   [X] 2.1.2. **During migration, ensure data is cleansed and transformed to its "pure" state.** For example, `ranked_list_json` should be converted to a proper JSON array of integers and stored correctly in the PostgreSQL `JSONB` column. Other data type inconsistencies or legacy issues should be addressed.
    *   [X] 2.1.3. Perform a test data migration with cleansing.
    *   [X] 2.1.4. Execute the final data migration with cleansing.

## Phase 3: Kubernetes (k3s/Rancher Desktop) Deployment

*   [ ] **3.1. Deploy PostgreSQL on Kubernetes (via Helm):**
    *   [ ] 3.1.1. Add a Helm repository for PostgreSQL (e.g., Bitnami: `helm repo add bitnami https://charts.bitnami.com/bitnami`).
    *   [ ] 3.1.2. Create a `postgresql-values.yaml` file to configure the PostgreSQL Helm chart (e.g., username, password, database name, persistence options, service type).
    *   [ ] 3.1.3. Deploy PostgreSQL using Helm: `helm install <release-name> bitnami/postgresql -f postgresql-values.yaml --namespace <your-namespace>`.
    *   [ ] 3.1.4. Verify PostgreSQL is running and accessible within the Kubernetes cluster. Note its service name and port.
*   [ ] **3.2. Containerize Backend Application:**
    *   [ ] 3.2.1. Create a `Dockerfile` in `llm-eval-platform/backend/` for the FastAPI application.
    *   [ ] 3.2.2. Build the backend Docker image: `docker build -t llm-eval-backend:latest ./llm-eval-platform/backend/`.
    *   [ ] 3.2.3. (Optional) Push the image to a container registry (Docker Hub, GCR, ECR, or local registry if k3s can access it). If using Rancher Desktop's k3s, it can often use locally built images directly.
*   [ ] **3.3. Containerize Frontend Application:**
    *   [ ] 3.3.1. Create a `Dockerfile` in `llm-eval-platform/frontend/` for the React application (likely a multi-stage build using Node.js to build static assets, then serving them with a lightweight web server like Nginx).
    *   [ ] 3.3.2. Build the frontend Docker image: `docker build -t llm-eval-frontend:latest ./llm-eval-platform/frontend/`.
    *   [ ] 3.3.3. (Optional) Push the image to a container registry.
*   [ ] **3.4. Create Kubernetes Manifests:**
    *   [ ] 3.4.1. **Namespace:** Create a `namespace.yaml` (e.g., `llm-eval-ns.yaml`) if you want a dedicated namespace.
    *   [ ] 3.4.2. **Backend ConfigMap:** Create `backend-configmap.yaml` to store non-sensitive configurations for the backend (e.g., database host (K8s service name for PG), database name, port, any external API URLs). The `DATABASE_URL` will be constructed here or via Secrets.
    *   [ ] 3.4.3. **Backend Secret:** Create `backend-secret.yaml` to store sensitive data like the database user and password, OpenRouter API key. (Remember to `base64` encode values for Secrets).
    *   [ ] 3.4.4. **Backend Deployment:** Create `backend-deployment.yaml` (references ConfigMap, Secret, and the backend Docker image).
    *   [ ] 3.4.5. **Backend Service:** Create `backend-service.yaml` (e.g., ClusterIP or NodePort type) to expose the backend deployment within the cluster.
    *   [ ] 3.4.6. **Frontend Deployment:** Create `frontend-deployment.yaml` (references the frontend Docker image). It might need a ConfigMap if it needs to know the backend API URL.
    *   [ ] 3.4.7. **Frontend Service:** Create `frontend-service.yaml` (e.g., LoadBalancer or NodePort type) to expose the frontend.
    *   [ ] 3.4.8. **(Optional) Ingress:** Create `ingress.yaml` if you have an Ingress controller setup in k3s (Rancher Desktop usually provides one like Traefik or Nginx Ingress) to route external traffic to your frontend and backend services based on hostnames or paths.
*   [ ] **3.5. Deploy Application to Kubernetes:**
    *   [ ] 3.5.1. Apply the namespace: `kubectl apply -f namespace.yaml` (if created).
    *   [ ] 3.5.2. Apply the ConfigMap and Secret: `kubectl apply -f backend-configmap.yaml -n <your-namespace>`, `kubectl apply -f backend-secret.yaml -n <your-namespace>`.
    *   [ ] 3.5.3. Apply the deployments: `kubectl apply -f backend-deployment.yaml -n <your-namespace>`, `kubectl apply -f frontend-deployment.yaml -n <your-namespace>`.
    *   [ ] 3.5.4. Apply the services: `kubectl apply -f backend-service.yaml -n <your-namespace>`, `kubectl apply -f frontend-service.yaml -n <your-namespace>`.
    *   [ ] 3.5.5. Apply Ingress: `kubectl apply -f ingress.yaml -n <your-namespace>` (if created).
*   [ ] **3.6. Verification and Troubleshooting:**
    *   [ ] 3.6.1. Check pod statuses: `kubectl get pods -n <your-namespace>`.
    *   [ ] 3.6.2. Check service statuses and access points: `kubectl get services -n <your-namespace>`.
    *   [ ] 3.6.3. Check Ingress status: `kubectl get ingress -n <your-namespace>`.
    *   [ ] 3.6.4. View logs for failing pods: `kubectl logs <pod-name> -n <your-namespace>`.
    *   [ ] 3.6.5. Test application accessibility through the exposed frontend service/ingress.

## Phase 4: (Optional) Helm Chart for Application

*   [ ] **4.1. Create Helm Chart Structure:**
    *   [ ] 4.1.1. Use `helm create llm-eval-chart` to generate a boilerplate chart.
    *   [ ] 4.1.2. Move/adapt the Kubernetes YAML manifests (from step 3.4) into the `templates/` directory of the chart.
    *   [ ] 4.1.3. Parameterize configurations in `values.yaml` (e.g., image tags, replica counts, service types, resource limits, Ingress rules, PostgreSQL connection details if not using an external chart dependency).
*   [ ] **4.2. (Optional) Add PostgreSQL as a Dependency (Subchart):**
    *   [ ] 4.2.1. Add the Bitnami PostgreSQL chart as a dependency in `Chart.yaml` of your application chart.
    *   [ ] 4.2.2. Configure PostgreSQL dependency values within your main chart's `values.yaml`.
*   [ ] **4.3. Test Helm Chart Deployment:**
    *   [ ] 4.3.1. Lint the chart: `helm lint ./llm-eval-chart`.
    *   [ ] 4.3.2. Perform a dry run: `helm install llm-eval-release ./llm-eval-chart --dry-run --debug -n <your-namespace>`.
    *   [ ] 4.3.3. Deploy the chart: `helm install llm-eval-release ./llm-eval-chart -n <your-namespace>`.
    *   [ ] 4.3.4. Test upgrade and rollback.

## Phase X: Application Logic Simplification (Post-Migration)

*   [X] **X.1. Review and Refactor Validation Logic:**
    *   [X] X.1.1. Identify and remove/simplify Pydantic validators or other data parsing/cleaning logic in the application (e.g., in `aggregation_service.py`, `schemas/task.py`) that are now redundant due to the cleansed data in PostgreSQL.
    *   [X] X.1.2. Ensure API contracts and internal data handling still behave as expected after simplifications.
    *   [X] X.1.3. Run thorough tests. 