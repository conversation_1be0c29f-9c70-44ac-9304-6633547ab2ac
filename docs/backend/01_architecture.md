# Backend Architecture

## 1. Overview

The backend for the LLM Evaluation Platform is built using FastAPI, a modern, high-performance Python web framework. It handles API requests from the frontend, orchestrates interactions with Large Language Models (LLMs) via OpenRouter, manages data persistence using SQLAlchemy with an SQLite database, and provides real-time updates through Server-Sent Events (SSE).

## 2. Core Technologies

-   **FastAPI:** For building robust and efficient APIs with automatic data validation (via Pydantic) and interactive documentation.
-   **Pydantic:** Used for defining data schemas, ensuring data validation and serialization/deserialization for API requests and responses.
-   **SQLAlchemy (Async):** Provides an Object-Relational Mapper (ORM) for interacting with the database in an asynchronous manner, compatible with FastAPI's async capabilities.
-   **SQLite:** The default relational database used for storing task information, generations, and evaluations. Managed via Alembic for migrations.
-   **OpenRouter API:** Acts as a gateway to various LLM providers, allowing the platform to use a diverse set of models for generation and evaluation.
-   **Asyncio:** Python's built-in library for asynchronous programming, leveraged throughout the backend for non-blocking I/O operations, especially when dealing with external API calls (to LLMs) and background tasks.
-   **HTTPX:** An asynchronous HTTP client used for making requests to the OpenRouter API.

## 3. Directory Structure & Key Modules

(Located under `llm-eval-platform/backend/app/`)

-   **`main.py`:** The entry point for the FastAPI application. It initializes the app, sets up CORS middleware, includes the main API router, and handles startup/shutdown events.
-   **`api/`:** Contains API routing logic.
    -   `api.py`: Aggregates various endpoint routers.
    -   `endpoints/tasks.py`: Defines API endpoints related to task creation, status retrieval, output generation, evaluation requests, and SSE streaming.
-   **`core/`:**
    -   `config.py`: Manages application settings and configurations, loading values from environment variables (e.g., API keys, database URL) using Pydantic's `BaseSettings`.
-   **`crud/`:** (Create, Read, Update, Delete) Contains functions that interact directly with the database models to perform data operations. Examples:
    -   `crud_task.py`: Manages task records.
    -   `crud_generation.py`: Manages generated output records.
    -   `crud_evaluation.py`: Manages evaluation session records.
    -   `crud_ranking.py`: Manages individual ranking results from evaluators.
-   **`db/`:**
    -   `base.py`: Initializes the SQLAlchemy async engine, session factory (`AsyncSessionFactory`), and the declarative base class (`Base`) for ORM models. Also includes the `get_db` dependency for FastAPI.
    -   `models.py`: Defines the SQLAlchemy ORM models (e.g., `Task`, `Generation`, `Evaluation`, `Ranking`) which map to database tables.
-   **`schemas/`:**
    -   `task.py`: Defines Pydantic models for data validation and serialization. These schemas are used in API endpoints to define request and response bodies, ensuring data consistency.
-   **`services/`:**
    -   `llm_service.py`: Contains the core business logic for interacting with LLMs. This includes:
        -   Constructing prompts for both generation and evaluation.
        -   Making asynchronous calls to the OpenRouter API using `httpx`.
        -   Handling streaming of LLM outputs and managing `asyncio.Queue`s for SSE.
        -   Orchestrating the fan-out of requests to multiple generator/evaluator models.
        -   Processing responses from LLMs, including parsing JSON results from evaluators.

## 4. Asynchronous Operations & Task Management

-   **FastAPI BackgroundTasks:** For long-running operations like LLM generation and evaluation, the backend utilizes FastAPI's `BackgroundTasks`. This allows the API to return an immediate response (e.g., task accepted) while the actual processing happens in the background without blocking the main application thread.
-   **Asyncio Gather:** Within the background tasks (in `llm_service.py`), `asyncio.gather` is used to make concurrent calls to multiple LLMs, significantly speeding up the N x M generation and evaluation process.
-   **Streaming via SSE:**
    -   The `llm_service.py` module uses `asyncio.Queue` to buffer text chunks received from streaming LLM generation APIs.
    -   The `/tasks/{task_id}/stream` endpoint in `tasks.py` reads from these queues and sends the data as Server-Sent Events to the connected frontend client, enabling real-time display of generated outputs.
    -   A dictionary (`task_event_queues`) in `llm_service.py` manages these queues per active task and model.

## 5. Database Management

-   **SQLAlchemy ORM:** Defines the database schema through Python classes (`models.py`).
-   **Async Operations:** All database interactions are performed asynchronously using `AsyncSession`.
-   **Session Management:** The `get_db` dependency provides a database session to API endpoints, ensuring sessions are properly managed and closed.
-   **Migrations:** Alembic is used for managing database schema migrations (though details are in `alembic/README.md`). Database tables are not created automatically by `Base.metadata.create_all` on startup in production to ensure migrations handle schema changes.

## 6. Configuration & Security

-   **Environment Variables:** Sensitive information like the `OPENROUTER_API_KEY` and `DATABASE_URL` is managed through environment variables, loaded via a `.env` file and Pydantic settings.
-   **SSL/TLS for LLM Calls:** The `llm_service.py` includes logic to use a company-specific SSL certificate (`SLB Issuing CA3.crt`) for requests to OpenRouter, with fallbacks for system certificates or insecure connections if the custom certificate is not found or fails.
-   **CORS:** Configured in `main.py` to allow requests from the frontend (currently set to all origins `"*"` for development).

## 7. Error Handling

-   FastAPI handles request validation errors automatically using Pydantic schemas.
-   Custom HTTPExceptions are raised for specific error conditions (e.g., task not found).
-   Within `llm_service.py`, errors during LLM API calls or response processing are caught, logged, and where appropriate, stored in the database (e.g., `Generation.error_message`, `Ranking.error_message`).
-   Background tasks also have try-except blocks to manage their lifecycle and update task statuses to FAILED if unrecoverable errors occur. 