[tool.poetry]
name = "llm-eval-backend"
version = "0.1.0"
description = "Backend for the LLM Evaluation Platform"
authors = ["Your Name <<EMAIL>>"]
readme = "../README.md" # Refers to the root README.md
package-mode = false

[tool.poetry.dependencies]
python = ">=3.12,<4.0"
fastapi = "^0.111.0"
uvicorn = {extras = ["standard"], version = "^0.29.0"}
sqlalchemy = {extras = ["asyncio"], version = "^2.0.30"}
asyncpg = "^0.29.0" # Async driver for PostgreSQL
pydantic = {extras = ["email"], version = "^2.7.1"}
pydantic-settings = "^2.2.1"
openai = "^1.30.1" # For interacting with OpenRouter (OpenAI compatible API)
python-dotenv = "^1.0.1"
alembic = "^1.13.1" # Optional: for database migrations
arq = "^0.26"      # Optional: Recommended for background tasks over FastAPI's default
certifi = "^2025.4.26"
scipy = "^1.15.3"
psycopg2-binary = "^2.9.0" # Synchronous PostgreSQL driver required by Alembic


[tool.poetry.group.dev.dependencies]
pytest = "^8.2.1"
pytest-asyncio = "^0.23.7"
httpx = "^0.27.0" # For testing FastAPI endpoints

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.pytest.ini_options]
pythonpath = ["."]
asyncio_mode = "auto" 