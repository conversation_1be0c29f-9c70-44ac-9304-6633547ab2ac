import asyncio
import json
import logging
import os
import sys # Add sys module

# Add the project root (backend directory) to sys.path to resolve app module
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
PROJECT_ROOT = os.path.dirname(SCRIPT_DIR)
sys.path.append(PROJECT_ROOT)

from sqlalchemy import create_engine, text, Enum as SQLEnum # Add Enum for status
from sqlalchemy.ext.asyncio import create_async_engine
from sqlalchemy.orm import sessionmaker, Session as SyncSession # Add SyncSession
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy import Column, Integer, String, Text, DateTime, JSON, ForeignKey, Boolean # Add Boolean
from app.db.models import TaskStatus # Assuming TaskStatus is in this path
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
log = logging.getLogger(__name__)

# Load environment variables from .env file
load_dotenv()

# Database URLs from environment variables
SQLALCHEMY_DATABASE_URL_POSTGRES = os.getenv("DATABASE_URL")
# The script will be run from backend/, so ./sql_app.db is correct relative to that
SQLALCHEMY_DATABASE_URL_SQLITE = os.getenv("SQLITE_DATABASE_URL", "sqlite:///./sql_app.db")


if not SQLALCHEMY_DATABASE_URL_POSTGRES:
    log.error("DATABASE_URL environment variable not set for PostgreSQL.")
    exit(1)
if not SQLALCHEMY_DATABASE_URL_SQLITE:
    log.error("SQLITE_DATABASE_URL environment variable not set and no default used.")
    exit(1)

# --- SQLAlchemy setup for SQLite (Source - Synchronous) ---
BaseSQLite = declarative_base()

class TaskSQLite(BaseSQLite):
    __tablename__ = "tasks"
    id = Column(Integer, primary_key=True, index=True)
    prompt = Column(Text, nullable=False)
    status = Column(SQLEnum(TaskStatus), default=TaskStatus.PENDING, nullable=False)
    created_at = Column(DateTime)
    updated_at = Column(DateTime)

class GenerationSQLite(BaseSQLite):
    __tablename__ = "generations"
    id = Column(Integer, primary_key=True, index=True)
    task_id = Column(Integer, ForeignKey("tasks.id"), nullable=False)
    model_id_used = Column(String, nullable=False, index=True)
    blind_id = Column(String, nullable=False, index=True, unique=True)
    output_text = Column(Text)
    reasoning_text = Column(Text, nullable=True)
    error_message = Column(Text, nullable=True)
    created_at = Column(DateTime)

class EvaluationSQLite(BaseSQLite):
    __tablename__ = "evaluations"
    id = Column(Integer, primary_key=True, index=True)
    task_id = Column(Integer, ForeignKey("tasks.id"), nullable=False)
    status = Column(SQLEnum(TaskStatus), default=TaskStatus.PENDING, nullable=False)
    evaluation_used_blind_ids = Column(Boolean, nullable=False, server_default='false')
    created_at = Column(DateTime)
    updated_at = Column(DateTime)

class RankingSQLite(BaseSQLite):
    __tablename__ = "rankings"
    id = Column(Integer, primary_key=True, index=True)
    evaluation_id = Column(Integer, ForeignKey("evaluations.id"), nullable=False)
    evaluator_model_id = Column(String, nullable=False, index=True)
    ranked_list_json = Column(JSON) # SQLite uses JSON
    reasoning_text = Column(Text)
    error_message = Column(Text, nullable=True)
    created_at = Column(DateTime)


sqlite_engine = create_engine(SQLALCHEMY_DATABASE_URL_SQLITE)
SessionLocalSQLite = sessionmaker(autocommit=False, autoflush=False, bind=sqlite_engine)

# --- SQLAlchemy setup for PostgreSQL (Target - Asynchronous) ---
# We will use raw SQL for inserts into PostgreSQL for simplicity in this script,
# but an async engine is set up if ORM usage is preferred later.
# For direct execution, SQLAlchemy Core or psycopg2 (via asyncpg) could also be used.
postgres_engine_async = create_async_engine(SQLALCHEMY_DATABASE_URL_POSTGRES)


async def clear_postgres_tables(conn_pg):
    log.info("Clearing existing data from PostgreSQL tables...")
    # Order matters due to foreign key constraints
    await conn_pg.execute(text("DELETE FROM rankings;"))
    await conn_pg.execute(text("DELETE FROM evaluations;"))
    await conn_pg.execute(text("DELETE FROM generations;"))
    await conn_pg.execute(text("DELETE FROM tasks;"))
    await conn_pg.commit()
    log.info("PostgreSQL tables cleared.")

async def migrate_data():
    db_sqlite: SyncSession = SessionLocalSQLite()
    
    # Using a single connection for all PG operations in this script
    async with postgres_engine_async.connect() as conn_pg:
        await clear_postgres_tables(conn_pg)

        # --- Migrate Tasks ---
        log.info("Migrating Tasks...")
        tasks_sqlite = db_sqlite.query(TaskSQLite).all()
        new_task_ids_map = {} # old_sqlite_id -> new_pg_id
        for task_s in tasks_sqlite:
            res = await conn_pg.execute(
                text("""
                    INSERT INTO tasks (prompt, status, created_at, updated_at)
                    VALUES (:prompt, :status, :created_at, :updated_at)
                    RETURNING id;
                """),
                parameters={
                    "prompt": task_s.prompt,
                    "status": task_s.status.value if task_s.status else None, # handle potential None status
                    "created_at": task_s.created_at,
                    "updated_at": task_s.updated_at,
                }
            )
            new_id = res.scalar_one()
            new_task_ids_map[task_s.id] = new_id
            # log.debug(f"Migrated Task ID {task_s.id} -> {new_id}")
        await conn_pg.commit()
        log.info(f"Migrated {len(tasks_sqlite)} Tasks.")

        # --- Migrate Generations ---
        log.info("Migrating Generations...")
        generations_sqlite = db_sqlite.query(GenerationSQLite).all()
        new_gen_ids_map = {} # old_sqlite_id -> new_pg_id
        # For mapping model names in rankings back to generation IDs in Postgres
        pg_generation_model_to_id_map = {} # (task_pg_id, model_id_used_str) -> gen_pg_id

        for gen_s in generations_sqlite:
            task_pg_id = new_task_ids_map.get(gen_s.task_id)
            if task_pg_id is None:
                log.warning(f"Skipping Generation ID {gen_s.id} due to missing Task ID {gen_s.task_id} in map.")
                continue
            
            res = await conn_pg.execute(
                text("""
                    INSERT INTO generations (task_id, model_id_used, blind_id, output_text, reasoning_text, error_message, created_at)
                    VALUES (:task_id, :model_id_used, :blind_id, :output_text, :reasoning_text, :error_message, :created_at)
                    RETURNING id;
                """),
                parameters={
                    "task_id": task_pg_id,
                    "model_id_used": gen_s.model_id_used,
                    "blind_id": gen_s.blind_id,
                    "output_text": gen_s.output_text,
                    "reasoning_text": gen_s.reasoning_text,
                    "error_message": gen_s.error_message,
                    "created_at": gen_s.created_at,
                }
            )
            new_id = res.scalar_one()
            new_gen_ids_map[gen_s.id] = new_id
            pg_generation_model_to_id_map[(task_pg_id, gen_s.model_id_used)] = new_id
            # log.debug(f"Migrated Generation ID {gen_s.id} -> {new_id}")
        await conn_pg.commit()
        log.info(f"Migrated {len(generations_sqlite)} Generations.")

        # --- Migrate Evaluations ---
        log.info("Migrating Evaluations...")
        evaluations_sqlite = db_sqlite.query(EvaluationSQLite).all()
        new_eval_ids_map = {} # old_sqlite_id -> new_pg_id
        # Store task_pg_id for each eval_pg_id for ranking cleansing
        pg_eval_to_task_map = {} # eval_pg_id -> task_pg_id

        for eval_s in evaluations_sqlite:
            task_pg_id = new_task_ids_map.get(eval_s.task_id)
            if task_pg_id is None:
                log.warning(f"Skipping Evaluation ID {eval_s.id} due to missing Task ID {eval_s.task_id} in map.")
                continue

            res = await conn_pg.execute(
                text("""
                    INSERT INTO evaluations (task_id, status, evaluation_used_blind_ids, created_at, updated_at)
                    VALUES (:task_id, :status, :evaluation_used_blind_ids, :created_at, :updated_at)
                    RETURNING id;
                """),
                parameters={
                    "task_id": task_pg_id,
                    "status": eval_s.status.value if eval_s.status else None,
                    "evaluation_used_blind_ids": eval_s.evaluation_used_blind_ids,
                    "created_at": eval_s.created_at,
                    "updated_at": eval_s.updated_at,
                }
            )
            new_id = res.scalar_one()
            new_eval_ids_map[eval_s.id] = new_id
            pg_eval_to_task_map[new_id] = task_pg_id
            # log.debug(f"Migrated Evaluation ID {eval_s.id} -> {new_id}")
        await conn_pg.commit()
        log.info(f"Migrated {len(evaluations_sqlite)} Evaluations.")

        # --- Migrate Rankings ---
        log.info("Migrating Rankings...")
        rankings_sqlite = db_sqlite.query(RankingSQLite).all()
        migrated_rankings_count = 0
        for rank_s in rankings_sqlite:
            eval_pg_id = new_eval_ids_map.get(rank_s.evaluation_id)
            if eval_pg_id is None:
                log.warning(f"Skipping Ranking ID {rank_s.id} due to missing Evaluation ID {rank_s.evaluation_id} in map.")
                continue

            task_pg_id_for_ranking = pg_eval_to_task_map.get(eval_pg_id)
            if task_pg_id_for_ranking is None:
                log.warning(f"Skipping Ranking ID {rank_s.id} as its parent Evaluation {eval_pg_id} could not be mapped to a Task.")
                continue

            cleaned_ranked_list = None
            if rank_s.ranked_list_json:
                try:
                    # ranked_list_json in SQLite could be a string or already a list from previous attempts
                    if isinstance(rank_s.ranked_list_json, str):
                        sqlite_ranked_list = json.loads(rank_s.ranked_list_json)
                    else:
                        sqlite_ranked_list = rank_s.ranked_list_json # Assume it's a list

                    if not isinstance(sqlite_ranked_list, list):
                        log.warning(f"Ranked list for SQLite Ranking ID {rank_s.id} is not a list: {sqlite_ranked_list}. Skipping ranking list.")
                        cleaned_ranked_list = None # Store as NULL
                    else:
                        cleaned_ranked_list = []
                        for item in sqlite_ranked_list:
                            if isinstance(item, int): # Assumed to be a generation_id already
                                # We need to map old gen_id to new_gen_id if IDs changed
                                # However, our PG insertion returns new IDs, so we should look up in new_gen_ids_map
                                # But, if it was already an ID, it was likely from the OLD sqlite db.
                                # The safest is to re-map from model name IF the item is a string.
                                # If it's an int, it *should* be a new_gen_id if data was consistent before.
                                # Let's assume for now that if it's an INT, it's a *SQLite* generation_id
                                gen_pg_id = new_gen_ids_map.get(item)
                                if gen_pg_id:
                                    cleaned_ranked_list.append(gen_pg_id)
                                else:
                                    log.warning(f"Could not map SQLite Generation ID {item} to a PostgreSQL ID for Ranking ID {rank_s.id}. Item skipped.")
                            elif isinstance(item, str): # Assumed to be a model_id_used string
                                model_name = item
                                gen_pg_id = pg_generation_model_to_id_map.get((task_pg_id_for_ranking, model_name))
                                if gen_pg_id:
                                    cleaned_ranked_list.append(gen_pg_id)
                                else:
                                    log.warning(f"Could not find PostgreSQL Generation ID for model '{model_name}' in Task {task_pg_id_for_ranking} (for Ranking ID {rank_s.id}). Item skipped.")
                            else:
                                log.warning(f"Unknown item type in ranked_list_json for Ranking ID {rank_s.id}: {item}. Item skipped.")
                        
                        if not cleaned_ranked_list: # If all items failed mapping or list was empty
                            cleaned_ranked_list = None


                except json.JSONDecodeError:
                    log.warning(f"Could not decode ranked_list_json for SQLite Ranking ID {rank_s.id}: {rank_s.ranked_list_json}. Storing as NULL.")
                    cleaned_ranked_list = None
                except Exception as e:
                    log.error(f"Error processing ranked_list_json for SQLite Ranking ID {rank_s.id}: {e}. Storing as NULL.")
                    cleaned_ranked_list = None
            
            # Convert list to JSON string for PostgreSQL JSONB
            final_ranked_list_json_str = json.dumps(cleaned_ranked_list) if cleaned_ranked_list is not None else None

            await conn_pg.execute(
                text("""
                    INSERT INTO rankings (evaluation_id, evaluator_model_id, ranked_list_json, reasoning_text, error_message, created_at)
                    VALUES (:evaluation_id, :evaluator_model_id, CAST(:ranked_list_json AS JSONB), :reasoning_text, :error_message, :created_at);
                """),
                parameters={
                    "evaluation_id": eval_pg_id,
                    "evaluator_model_id": rank_s.evaluator_model_id,
                    "ranked_list_json": final_ranked_list_json_str,
                    "reasoning_text": rank_s.reasoning_text,
                    "error_message": rank_s.error_message,
                    "created_at": rank_s.created_at,
                }
            )
            migrated_rankings_count += 1
        await conn_pg.commit()
        log.info(f"Migrated {migrated_rankings_count} Rankings.")

    db_sqlite.close()
    await postgres_engine_async.dispose()
    log.info("Data migration completed.")

if __name__ == "__main__":
    asyncio.run(migrate_data()) 