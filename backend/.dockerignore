# Python cache
__pycache__
*.pyc
*.pyo
*.pyd
.Python

# Virtual environments
.venv
venv/
ENV/
env/

# Poetry
.poetry

# Testing
.pytest_cache
.coverage
htmlcov/
.tox/

# IDE files
.vscode
.idea
*.swp
*.swo

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git
.gitignore

# Logs
logs/
*.log

# Database
*.db
*.sqlite
*.sqlite3

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Docker
Dockerfile*
.dockerignore

# Documentation
README.md
docs/

# CI/CD
.github

# Alembic
# alembic.ini - REMOVED: We need this file in the container for migrations

# Scripts
scripts/ 