import logging
import asyncio
import json
from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks, Request, Query
from fastapi.responses import StreamingResponse
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, AsyncGenerator, Dict, Optional

from app.db.base import get_db, AsyncSessionFactory
from app.schemas import task as task_schema
from app.services import llm_service
from app.crud import crud_task, crud_evaluation, crud_generation, crud_ranking
from app.db import models
from app.core.config import settings
from app.services.aggregation_service import aggregation_service

log = logging.getLogger(__name__)
router = APIRouter()

# Placeholder list of models - fetch dynamically or from config later
# This could be moved to settings or a dedicated service
AVAILABLE_MODELS = settings.OPENROUTER_AVAILABLE_MODELS if hasattr(settings, 'OPENROUTER_AVAILABLE_MODELS') and settings.OPENROUTER_AVAILABLE_MODELS else [
    "openai/gpt-4.1",
    "openai/gpt-4o",
    "openai/chatgpt-4o-latest",
    "openai/gpt-4.1-mini",
    "openai/gpt-4.1-nano",
    "anthropic/claude-sonnet-4",
    "anthropic/claude-3.7-sonnet",
    "google/gemini-2.5-pro-preview",
    "google/gemini-2.5-flash-preview-05-20",
    "deepseek/deepseek-r1",
    "deepseek/deepseek-chat-v3-0324",
    "qwen/qwen3-235b-a22b",
    "qwen/qwen3-30b-a3b",
    "x-ai/grok-3-beta",
]

@router.get("/models", response_model=task_schema.ModelListResponse)
async def get_available_models():
    """
    Retrieve a list of available LLM models for generation and evaluation.
    """
    log.info("Fetching available models list.")
    return {"models": AVAILABLE_MODELS}

@router.post("", response_model=task_schema.TaskCreateResponse, status_code=status.HTTP_202_ACCEPTED)
async def create_task_endpoint(
    task_in: task_schema.TaskCreateRequest,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db)
):
    """
    Create a new task to generate outputs from multiple LLMs based on the prompt.
    Triggers background generation.
    """
    log.info(f"Received task request: {task_in.prompt[:50]}...")

    # Determine the actual models to use
    models_to_use = task_in.models_to_generate or settings.DEFAULT_GENERATION_MODELS
    if not models_to_use:
        raise HTTPException(status_code=400, detail="No models specified and no default models configured.")

    db_task = await crud_task.create_task(
        db=db, 
        task_in=task_schema.TaskCreate(
            prompt=task_in.prompt,
            system_prompt=task_in.system_prompt,  # Add system_prompt field
            models_to_generate=models_to_use
        )
    )

    # Start background task for generation
    background_tasks.add_task(
        llm_service.generate_outputs_for_task_background, # Correct function name
        task_id=db_task.id, 
        prompt=db_task.prompt,
        models_to_use=models_to_use, # Correct argument name
        system_prompt=db_task.system_prompt,  # Pass system_prompt to background task
        # db_session_factory removed as the background task creates its own session
    )

    log.info(f"Created task {db_task.id} with models: {models_to_use}")
    return task_schema.TaskCreateResponse(
        task_id=db_task.id,
        status=db_task.status, 
        requested_models=models_to_use, # Return the models being used
        message="Task created and generation started."
    )


@router.get("/{task_id}/status", response_model=task_schema.Task)
async def get_task_status_endpoint(task_id: int, db: AsyncSession = Depends(get_db)):
    """
    Retrieve the full task details including status, generations, and evaluations.
    """
    log.info(f"--- GET /{task_id}/status ENTERED (Type: {type(task_id)}) ---")
    log.info(f"Fetching full status for task ID: {task_id}")
    # Use a comprehensive fetch that includes generations and evaluations with rankings
    db_task = await db.get(models.Task, task_id, options=[
        selectinload(models.Task.generations),
        selectinload(models.Task.evaluations).selectinload(models.Evaluation.rankings)
    ])

    if not db_task:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Task not found")
    return db_task


@router.get("/{task_id}/outputs", response_model=task_schema.TaskStatusResponse) # Kept for compatibility if frontend uses this
async def get_task_outputs_endpoint(task_id: int, db: AsyncSession = Depends(get_db)):
    """
    Retrieve the current status and generated outputs for a specific task.
    (Consider deprecating in favor of /{task_id}/status which returns more info)
    """
    log.info(f"Fetching outputs for task ID: {task_id}")
    db_task = await crud_task.get_task_with_generations(db=db, task_id=task_id)
    if not db_task:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Task not found")
    
    # Adapt to the TaskStatusResponse schema
    return task_schema.TaskStatusResponse(
        task_id=db_task.id,
        status=db_task.status,
        generations=[task_schema.Generation.model_validate(gen) for gen in db_task.generations]
    )


@router.post("/{task_id}/evaluate", response_model=task_schema.EvaluateResponse, status_code=status.HTTP_202_ACCEPTED)
async def evaluate_task_outputs_endpoint(
    task_id: int,
    eval_request: task_schema.EvaluateRequest,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db)
):
    """
    Trigger evaluation of a task's outputs using selected LLMs as evaluators.
    """
    log.info(f"Received evaluation request for task ID: {task_id} using evaluators: {eval_request.evaluator_models}")

    db_task = await crud_task.get_task(db=db, task_id=task_id)
    if not db_task:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Task not found")
    
    if db_task.status not in [task_schema.TaskStatusEnum.COMPLETED, task_schema.TaskStatusEnum.EVALUATION_DONE, task_schema.TaskStatusEnum.FAILED]:
         # Allow re-evaluation even if failed before, or already evaluated.
         # Frontend should control if re-evaluation is sensible based on task state.
         log.warning(f"Task {task_id} status is {db_task.status}, proceeding with evaluation request anyway.")

    if not eval_request.evaluator_models:
        log.warning(f"Task {task_id}: No evaluator models provided in evaluation request.")
        raise HTTPException(status_code=400, detail="No evaluator models provided.")

    # Create Evaluation record in DB
    db_evaluation = await crud_evaluation.create_evaluation(
        db=db, 
        task_id=task_id,
        evaluation_used_blind_ids=eval_request.evaluation_used_blind_ids, # Pass the flag from request
        evaluation_prompt=eval_request.custom_evaluation_prompt  # Pass custom evaluation prompt
    )
    if not db_evaluation:
        log.error(f"Failed to create evaluation record in DB for task {task_id}")
        raise HTTPException(status_code=500, detail="Failed to create evaluation record in DB")

    log.info(f"Evaluation record created: ID {db_evaluation.id} for task {task_id}")

    # Update task status to EVALUATING
    db_task.status = task_schema.TaskStatusEnum.EVALUATING
    db.add(db_task)
    await db.commit()
    await db.refresh(db_task)
    log.info(f"Task {task_id} status updated to {task_schema.TaskStatusEnum.EVALUATING}.")

    log.info(f"Adding background task to evaluate task {task_id} with {eval_request.evaluator_models}")
    background_tasks.add_task(
        llm_service.evaluate_outputs_for_task_background, 
        task_id=task_id,
        evaluation_id=db_evaluation.id,
        evaluator_models=eval_request.evaluator_models,
        evaluation_used_blind_ids=eval_request.evaluation_used_blind_ids, # Pass the flag to background task
        custom_evaluation_prompt=eval_request.custom_evaluation_prompt  # Pass custom prompt
    )
    log.info(f"Background evaluation task added for evaluation ID {db_evaluation.id}")

    return task_schema.EvaluateResponse(
        evaluation_id=db_evaluation.id,
        status=db_evaluation.status, # Should be PENDING initially
        message="Evaluation started in background."
    )


@router.get("/evaluations/{evaluation_id}/report", response_model=task_schema.EvaluationReportResponse)
async def get_evaluation_report_endpoint(evaluation_id: int, db: AsyncSession = Depends(get_db)):
    """
    Retrieve the evaluation report containing rankings and reasons from evaluator models.
    """
    log.info(f"Fetching report for evaluation ID: {evaluation_id}")
    db_evaluation = await crud_evaluation.get_evaluation_with_rankings_and_task(db=db, evaluation_id=evaluation_id) # Modified CRUD call
    
    if not db_evaluation or not db_evaluation.task:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Evaluation or associated Task not found")

    # De-anonymize or prepare ranked list for display
    processed_rankings: List[task_schema.Ranking] = []
    if db_evaluation.rankings:
        # Fetch all relevant generations for this task once for efficient lookup
        generations_for_task = await crud_generation.get_generations_for_task(db=db, task_id=db_evaluation.task_id)
        generation_map: Dict[int, models.Generation] = {gen.id: gen for gen in generations_for_task}

        for db_rank in db_evaluation.rankings:
            display_ranked_list: List[str] = []
            error_in_current_rank_processing = False
            if db_rank.ranked_list_json: # This is List[int] of generation_ids
                for gen_id in db_rank.ranked_list_json:
                    generation_obj = generation_map.get(gen_id)
                    if not generation_obj:
                        log.error(f"Could not find generation object for ID {gen_id} in ranking {db_rank.id}")
                        error_in_current_rank_processing = True # Mark error, skip this ranking list or handle
                        display_ranked_list = [] # Clear if any part is unresolvable
                        break
                    
                    # Use evaluation-specific flag for de-anonymization
                    if db_evaluation.evaluation_used_blind_ids and generation_obj.blind_id:
                        display_ranked_list.append(generation_obj.blind_id)
                    else:
                        display_ranked_list.append(generation_obj.model_id_used)
            
            # Create a Pydantic model for the ranking, handling potential errors
            # The original db_rank.ranked_list_json (List[int]) isn't directly sent to frontend for this specific field.
            # Instead, display_ranked_list (List[str]) is what the frontend might expect for display.
            # However, our schema for Ranking.ranked_list_json is List[int].
            # This means the frontend will receive generation_ids and will need to map them itself.
            # For simplicity here, we will adhere to the schema. The frontend will do the mapping.
            # The `aggregation_service` will also work with generation_ids from `ranked_list_json`.

            # If error_in_current_rank_processing, we might want to alter db_rank for the response
            # For now, let schema validation handle it or pass as is. The important part is that
            # the aggregation service will use the gen_ids directly from ranked_list_json.
            
            # The current `task_schema.Ranking.model_validate(db_rank)` expects ranked_list_json to be List[int] or List[str]
            # based on schema. We changed schema to List[int]. So, no transformation needed here for this field.
            # The `display_ranked_list` logic above is more for if the API itself was to transform it to strings.
            # Let's ensure `ranked_list_json` is correctly passed as `List[int]` or `None`.

            processed_rankings.append(task_schema.Ranking.model_validate(db_rank))

    # Adapt to the EvaluationReportResponse schema
    return task_schema.EvaluationReportResponse(
        evaluation_id=db_evaluation.id,
        task_id=db_evaluation.task_id,
        status=db_evaluation.status,
        rankings=processed_rankings # Use the processed list
    )

# --- SSE Streaming Endpoint --- 
@router.get("/{task_id}/stream")
async def stream_task_output(task_id: int, request: Request):
    """
    Endpoint for Server-Sent Events (SSE) to stream generation progress.
    """
    log.info(f"SSE connection requested for task {task_id}")

    async def event_generator() -> AsyncGenerator[str, None]:
        MAX_QUEUE_WAIT_ATTEMPTS = 5 # Max attempts to wait for queue creation
        QUEUE_WAIT_DELAY = 0.5      # Delay in seconds between attempts

        queues: Dict[str, asyncio.Queue] = {}
        for attempt in range(MAX_QUEUE_WAIT_ATTEMPTS):
            queues = await llm_service.get_task_queues(task_id)
            if queues: # Check if queues dict is not empty
                log.info(f"[SSE Task {task_id}] Queues found on attempt {attempt + 1}.")
                break
            log.warning(f"[SSE Task {task_id}] Queues not yet available (attempt {attempt + 1}/{MAX_QUEUE_WAIT_ATTEMPTS}). Waiting...")
            await asyncio.sleep(QUEUE_WAIT_DELAY)
        else: # After loop, if queues are still not found
            log.error(f"[SSE Task {task_id}] Failed to find active queues after {MAX_QUEUE_WAIT_ATTEMPTS} attempts. Closing stream.")
            yield f"event: error\ndata: {json.dumps({'model': 'system', 'status': 'ERROR', 'content': 'Task setup timed out or task does not exist.'})}\n\n"
            return

        active_model_queues = list(queues.items())
        log.info(f"Starting SSE stream for task {task_id} with models: {[m for m,_ in active_model_queues]}")

        # Implement a round-robin queue read across each model for fair interleaving
        model_queues = {model_id: queue for model_id, queue in active_model_queues}
        finished_models = set()
        log.info(f"[SSE Task {task_id}] Fair interleaving start for models: {list(model_queues.keys())}")

        while len(finished_models) < len(model_queues):
            yielded_any = False
            # Iterate in fixed order for round-robin
            for model_id, queue in model_queues.items():
                if model_id in finished_models:
                    continue
                try:
                    event_data = queue.get_nowait()
                    queue.task_done()
                except asyncio.QueueEmpty:
                    continue
                # Handle CLOSED marker (stop reading further for this model)
                if event_data.get("status") == "CLOSED":
                    finished_models.add(model_id)
                    continue
                # Determine gate event type
                status = event_data.get("status")
                event_type = "message"
                if status == "DONE":
                    event_type = "done"
                elif status == "ERROR":
                    event_type = "error"
                sse_message = f"event: {event_type}\ndata: {json.dumps(event_data)}\n\n"
                # log.info(f"[SSE Task {task_id}] Yielding (fair) for model {model_id}: {event_type}, data: {json.dumps(event_data)[:100]}...")
                yield sse_message
                yielded_any = True
                # If terminal, mark as finished
                if status in ("DONE", "ERROR"):  
                    finished_models.add(model_id)
            # If no model had data this cycle, send a keep-alive
            if not yielded_any:
                yield ": keep-alive\n\n"
                # Small sleep to avoid tight loop
                await asyncio.sleep(0.2)
        log.info(f"[SSE Task {task_id}] Fair interleaving complete for task")

    async def _queue_reader(model_id: str, queue: asyncio.Queue, task_id: int) -> str | None: # Add task_id for logging
        """Reads one item from a queue and formats it as SSE.
           Returns None if the queue sends a CLOSED signal.
        """
        task = asyncio.current_task()
        if task: 
            task.set_name(f"reader_{model_id}") # Helps identify tasks
        try:
            event_data = await asyncio.wait_for(queue.get(), timeout=20) # Timeout to prevent indefinite wait
            queue.task_done()

            if event_data.get("status") == "CLOSED":
                 log.debug(f"Queue reader for {model_id} received CLOSED signal.")
                 return None # Signal to stop reading this queue

            # Determine event type based on status
            event_type = "message" # Default for chunks
            if event_data.get("status") == "DONE":
                event_type = "done"
            elif event_data.get("status") == "ERROR":
                event_type = "error"
            
            # Format as SSE message
            sse_message = f"event: {event_type}\ndata: {json.dumps(event_data)}\n\n"
            log.info(f"[SSE Task {task_id}] Yielding for model {model_id}: {event_type}, data: {json.dumps(event_data)[:100]}...")
            return sse_message
        except asyncio.TimeoutError:
            log.debug(f"Queue reader for {model_id} timed out waiting for item.")
            return ": queue-timeout\n\n" # Send a comment back
        except Exception as e:
             log.exception(f"Error reading from queue for model {model_id} in task {task_id}")
             return f"event: error\ndata: {json.dumps({'model': model_id, 'error': f'Queue read error: {e}'})}\n\n"

    headers = {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'X-Accel-Buffering': 'no' # Useful for Nginx proxying
    }
    return StreamingResponse(event_generator(), headers=headers)

# --- End SSE Streaming Endpoint --- 

# Need to import selectinload for the new /status endpoint
from sqlalchemy.orm import selectinload 

# --- History Endpoint ---
@router.get("/history", response_model=task_schema.TaskHistoryResponse)
async def get_task_history(
    db: AsyncSession = Depends(get_db),
    skip: int = Query(0, ge=0), # Add pagination query parameters
    limit: int = Query(50, ge=1, le=200) # Default limit 50, max 200
):
    """
    Retrieve a list of past tasks, ordered by creation date (newest first).
    """
    log.info(f"Fetching task history: skip={skip}, limit={limit}")
    db_tasks = await crud_task.get_tasks_for_history(db=db, skip=skip, limit=limit)
    
    # Map DB models to the TaskHistoryItem schema, creating snippets
    history_items = [
        task_schema.TaskHistoryItem(
            id=task.id,
            prompt_snippet=task.prompt[:80] + ("..." if len(task.prompt) > 80 else ""), # Create snippet
            status=task.status,
            created_at=task.created_at
        )
        for task in db_tasks
    ]
    
    return task_schema.TaskHistoryResponse(history=history_items)

# Added imports for delete endpoint
from sqlalchemy import select, delete
# from sqlalchemy.orm import selectinload # Already imported above

# --- Delete Task Endpoint ---
@router.delete("/{task_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_task_endpoint(task_id: int, db: AsyncSession = Depends(get_db)):
    """
    Delete a task and all its associated generations, evaluations, and rankings.
    """
    log.info(f"Received request to delete task ID: {task_id}")
    deleted = await crud_task.delete_task_recursive(db=db, task_id=task_id)
    if not deleted:
        log.warning(f"Task ID {task_id} not found for deletion.")
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Task not found")
    log.info(f"Task ID {task_id} deleted successfully.")
    # No content to return on successful deletion
    return None
# --- End Delete Task Endpoint --- 

# --- Evaluation Aggregation Endpoint ---
@router.post(
    "/evaluations/{evaluation_id}/aggregate", 
    response_model=task_schema.EvaluationAggregationResponse,
    summary="Aggregate Evaluation Rankings",
    tags=["Tasks & Evaluations"]
)
async def aggregate_evaluation_results_endpoint(
    evaluation_id: int,
    request_body: task_schema.EvaluationAggregationRequest, # Algorithm choice in request body
    db: AsyncSession = Depends(get_db)
):
    """
    Aggregates the rankings for a given evaluation using the specified algorithm.
    Calculates consistency scores for each evaluator against the aggregated list.
    """
    log.info(f"Received request to aggregate evaluation ID: {evaluation_id} using algorithm: {request_body.algorithm.value}")
    
    aggregation_result: Optional[task_schema.EvaluationAggregationResponse] = None
    try:
        aggregation_result = await aggregation_service.aggregate_evaluation_results(
            db=db, 
            evaluation_id=evaluation_id, 
            algorithm=request_body.algorithm,
            evaluator_weights=request_body.evaluator_weights # Pass weights
        )
    except ValueError as ve: # Catches "Evaluation X not found" if service raises it before returning a response object
        log.warning(f"Initial ValueError during aggregation call for eval {evaluation_id}: {str(ve)}")
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(ve))
    except NotImplementedError as nie:
        log.error(f"NotImplementedError for aggregation algorithm: {str(nie)}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(nie))
    except Exception as e: # Generic catch-all for unexpected errors from the service call itself
        log.exception(f"Unexpected error calling aggregation service for eval {evaluation_id}: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="An unexpected error occurred calling the aggregation service.")

    # Now, handle the returned aggregation_result
    if aggregation_result:
        if aggregation_result.error_message and not aggregation_result.aggregated_rankings:
            current_error_detail = str(aggregation_result.error_message)
            log.error(f"Aggregation service reported failure for evaluation {evaluation_id}: {current_error_detail}")
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=current_error_detail)
        
        log.info(f"Successfully aggregated evaluation {evaluation_id}.")
        return aggregation_result
    else:
        # This case should ideally not be reached if the service always returns a response or raises an error caught above.
        log.error(f"Aggregation service returned None or an unexpected response for eval {evaluation_id}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Aggregation service provided an invalid response.")
# --- End Evaluation Aggregation Endpoint --- 