from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text
from app.db.base import get_db
import logging
from datetime import datetime
from pydantic import BaseModel
from enum import Enum

router = APIRouter()
log = logging.getLogger(__name__)

class HealthStatus(str, Enum):
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"
    UNKNOWN = "unknown"

class HealthResponse(BaseModel):
    status: HealthStatus
    timestamp: datetime
    version: str
    database: dict
    services: dict

@router.get("/health", response_model=HealthResponse)
async def health_check(db: AsyncSession = Depends(get_db)):
    """
    System health check endpoint.
    Returns overall system status and component health.
    """
    try:
        # Check database connectivity
        try:
            result = await db.execute(text("SELECT 1"))
            db_status = HealthStatus.HEALTHY
            db_message = "Database connection successful"
        except Exception as e:
            log.error(f"Database health check failed: {e}")
            db_status = HealthStatus.UNHEALTHY
            db_message = f"Database connection failed: {str(e)}"

        # Check if we can query tasks table
        try:
            result = await db.execute(text("SELECT COUNT(*) FROM tasks"))
            task_count = result.scalar()
            db_tasks_status = HealthStatus.HEALTHY
            db_tasks_message = f"Tasks table accessible ({task_count} tasks)"
        except Exception as e:
            log.error(f"Tasks table health check failed: {e}")
            db_tasks_status = HealthStatus.UNHEALTHY
            db_tasks_message = f"Tasks table access failed: {str(e)}"

        # Determine overall status
        if db_status == HealthStatus.HEALTHY and db_tasks_status == HealthStatus.HEALTHY:
            overall_status = HealthStatus.HEALTHY
        elif db_status == HealthStatus.UNHEALTHY or db_tasks_status == HealthStatus.UNHEALTHY:
            overall_status = HealthStatus.UNHEALTHY
        else:
            overall_status = HealthStatus.DEGRADED

        return HealthResponse(
            status=overall_status,
            timestamp=datetime.utcnow(),
            version="1.0.0",  # You can make this dynamic
            database={
                "connection": {
                    "status": db_status,
                    "message": db_message
                },
                "tables": {
                    "status": db_tasks_status,
                    "message": db_tasks_message
                }
            },
            services={
                "api": {
                    "status": HealthStatus.HEALTHY,
                    "message": "API server responding"
                }
            }
        )

    except Exception as e:
        log.error(f"Health check failed: {e}")
        return HealthResponse(
            status=HealthStatus.UNHEALTHY,
            timestamp=datetime.utcnow(),
            version="1.0.0",
            database={
                "connection": {
                    "status": HealthStatus.UNKNOWN,
                    "message": f"Health check error: {str(e)}"
                },
                "tables": {
                    "status": HealthStatus.UNKNOWN,
                    "message": "Unable to check table status"
                }
            },
            services={
                "api": {
                    "status": HealthStatus.DEGRADED,
                    "message": "API responding but health check failed"
                }
            }
        ) 