import asyncio
from collections import defaultdict # Import defaultdict
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Dict, Any, AsyncGenerator, Optional # Add AsyncGenerator and Optional
from openai import AsyncOpenAI
import json
import logging # Add logging import
import certifi # Add certifi import
import httpx # Add httpx import
import os
import uuid # Added for blind_id generation
import openai # Add openai import

from app.core.config import settings
from app.db.base import AsyncSessionFactory # Import the session factory
from app.schemas import task as task_schema
from app.crud import crud_task, crud_generation, crud_evaluation, crud_ranking
from app.db.models import TaskStatus # For direct enum usage

log = logging.getLogger(__name__) # Get logger instance

# --- Streaming Setup ---
# Dictionary to hold asyncio Queues for each active task's streaming output.
# Key: task_id, Value: Queue
# The queue will hold dictionaries like: {"model": model_name, "chunk": text_chunk}
# Or special markers like: {"model": model_name, "status": "DONE" / "ERROR", "content": message}
task_event_queues: Dict[int, Dict[str, asyncio.Queue]] = defaultdict(dict)
# Lock for thread-safe access to the queues dictionary
queues_lock = asyncio.Lock()

# Helper to safely get or create a queue for a specific model within a task
async def _get_or_create_model_queue(task_id: int, model_id: str) -> asyncio.Queue:
    async with queues_lock:
        if model_id not in task_event_queues[task_id]:
            task_event_queues[task_id][model_id] = asyncio.Queue()
        return task_event_queues[task_id][model_id]

# Helper to get all queues for a task
async def get_task_queues(task_id: int) -> Dict[str, asyncio.Queue]:
     async with queues_lock:
        # Return a copy to avoid modification issues outside the lock
        return task_event_queues.get(task_id, {}).copy()

# Helper to remove all queues for a task when done
async def remove_task_queues(task_id: int):
    async with queues_lock:
        if task_id in task_event_queues:
            # Clear queues first to signal any listeners
            for model_id, queue in task_event_queues[task_id].items():
                 try:
                     # Add a final marker if not already done
                     await queue.put({"model": model_id, "status": "CLOSED"}) 
                 except Exception:
                     pass # Ignore errors if queue is already closed or full
            # Remove the task entry
            del task_event_queues[task_id]
            log.info(f"[Task BG {task_id}] Removed event queues.")
# --- End Streaming Setup ---

# Specify company certificate path
COMPANY_CERT_PATH = os.path.expanduser("/root/projects/report/SLB Issuing CA3.crt")

# --- OpenRouter Client Setup ---

# --- End OpenRouter Client Setup ---

# Configure OpenAI Client for OpenRouter
async def get_openai_client() -> AsyncOpenAI:
    # Add recommended headers for OpenRouter
    headers = {
        "HTTP-Referer": "http://localhost",
        "X-Title": "LLM Eval Platform"
    }
    
    # # Check if company certificate exists
    # if os.path.exists(COMPANY_CERT_PATH):
    #     log.info(f"Using company certificate at: {COMPANY_CERT_PATH}")
    #     # Use the certificate file for verification
    #     custom_transport = httpx.AsyncHTTPTransport(verify=COMPANY_CERT_PATH)
    # else:
    #     log.warning(f"Company certificate not found at: {COMPANY_CERT_PATH}. Using system certificates.")
    #     # Try with system certificates first
    #     custom_transport = httpx.AsyncHTTPTransport(verify=True)
        
    # # First try with proper verification
    # try:
    #     custom_client = httpx.AsyncClient(transport=custom_transport)
    #     client = AsyncOpenAI(
    #         base_url="https://openrouter.ai/api/v1",
    #         api_key=settings.OPENROUTER_API_KEY,
    #         default_headers=headers,
    #         http_client=custom_client
    #     )
    #     # Test the client with a simple request
    #     # await client.models.list() # Test request can be kept commented
    #     return client
    # except Exception as e:
    #     log.warning(f"SSL verification failed with error: {str(e)}. Falling back to insecure connection.")
    #     # Fallback to insecure connection if the above fails

    log.warning("SSL verification is DISABLED. This is insecure and only for specific environments!")
    insecure_transport = httpx.AsyncHTTPTransport(verify=False)
    insecure_client = httpx.AsyncClient(transport=insecure_transport)
    return AsyncOpenAI(
        base_url="https://openrouter.ai/api/v1",
        api_key=settings.OPENROUTER_API_KEY,
        default_headers=headers,
        http_client=insecure_client
    )

# Modified to stream output into a queue
async def generate_single_output_stream(
    client: AsyncOpenAI, 
    task_id: int, 
    model: str, 
    prompt: str,
    system_prompt: Optional[str] = None  # Add system_prompt parameter
) -> Dict[str, Any]:
    """Generates output from a single model, streaming chunks into a task-specific queue."""
    log.info(f"[Task BG {task_id}] Starting stream generation from model: {model}...")
    output_queue = await _get_or_create_model_queue(task_id, model)
    full_response = ""
    full_reasoning_text = "" # New: To aggregate reasoning
    error_msg = None
    usage_stats = {}  # To store usage statistics

    try:
        # Prepare messages with system prompt first
        messages = []
        # Use provided system prompt or default
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})
        else:
            # Default system prompt
            messages.append({"role": "system", "content": "You are a helpful assistant. Please provide clear, accurate, and well-structured responses."})
        
        # Then add the user prompt
        messages.append({"role": "user", "content": prompt})

        stream = await client.chat.completions.create(
            model=model,
            messages=messages,
            timeout=120.0,
            stream=True, # Enable streaming,
            extra_body={ # This extra_body might be specific to certain OpenRouter models/features
                "reasoning": {
                    "effort": "medium",
                    "exclude": False
                },
                "usage": {
                    "include": True  # Enable usage tracking
                }
    }
        )
        async for chunk in stream:
            # log.info(f"[Task BG {task_id}] delta from {model}: {chunk.choices[0].delta}") # Can be very verbose
            
            event_payload: Dict[str, Any] = {"model": model}
            send_event = False

            content = chunk.choices[0].delta.content
            if content:
                full_response += content
                event_payload["chunk"] = content
                send_event = True
            
            # Check for reasoning attribute as per user's log
            if hasattr(chunk.choices[0].delta, "reasoning") and chunk.choices[0].delta.reasoning:
                reasoning_content = str(chunk.choices[0].delta.reasoning) # Ensure it's a string
                # log.info(f"[Task BG {task_id}] Reasoning from {model}: {reasoning_content[:100]}...") # Log snippet
                full_reasoning_text += reasoning_content # Directly concatenate
                event_payload["reasoning_detail"] = reasoning_content # Still send original to frontend for live update if needed
                send_event = True
            
            # Capture usage statistics from the final chunk
            if hasattr(chunk, 'usage') and chunk.usage:
                usage_stats = {
                    "prompt_tokens": getattr(chunk.usage, 'prompt_tokens', None),
                    "completion_tokens": getattr(chunk.usage, 'completion_tokens', None),
                    "total_tokens": getattr(chunk.usage, 'total_tokens', None),
                    "reasoning_tokens": getattr(getattr(chunk.usage, 'completion_tokens_details', None), 'reasoning_tokens', None) if hasattr(chunk.usage, 'completion_tokens_details') else None,
                    "cached_tokens": getattr(getattr(chunk.usage, 'prompt_tokens_details', None), 'cached_tokens', None) if hasattr(chunk.usage, 'prompt_tokens_details') else None,
                    "cost_credits": getattr(chunk.usage, 'cost', None),
                    "generation_id": getattr(chunk, 'id', None),  # Moved generation ID capture here
                }
                log.info(f"[Task BG {task_id}] Usage stats captured for {model}: {usage_stats}")
            
            # Capture generation ID separately if not captured above
            if hasattr(chunk, 'id') and chunk.id and not usage_stats.get("generation_id"):
                usage_stats["generation_id"] = chunk.id
            
            if send_event:
                await output_queue.put(event_payload)
                # Optional: small sleep to prevent overwhelming the queue/network
                # await asyncio.sleep(0.01) 

        log.info(f"[Task BG {task_id}] Successfully finished streaming from {model}")
        await output_queue.put({"model": model, "status": "DONE", "content": None}) # Signal stream end

    except openai.APIError as e:
        error_body = None
        if hasattr(e, 'response') and e.response and hasattr(e.response, 'text'):
            error_body = e.response.text
        elif hasattr(e, 'body') and e.body:
            error_body = e.body
        
        detailed_error_msg = f"Error streaming from {model}: {type(e).__name__} - {e}. Response body: {error_body}"
        log.exception(f"[Task BG {task_id}] {detailed_error_msg}") # Log full exception info with body
        # Put error message into the queue
        await output_queue.put({"model": model, "status": "ERROR", "content": detailed_error_msg})
        error_msg = detailed_error_msg # Update error_msg to include details for DB

    except Exception as e:
        error_msg = f"Error streaming from {model}: {type(e).__name__} - {e}"
        log.exception(f"[Task BG {task_id}] Error streaming from {model}") # Log full exception info

    # Return the final aggregated result for DB storage (or error)
    result = {
        "output_text": full_response if not error_msg else None,
        "reasoning_text": full_reasoning_text if not error_msg and full_reasoning_text else None, # New
        "error_message": error_msg
    }
    
    # Add usage statistics to the result
    result.update(usage_stats)
    
    return result


async def generate_outputs_for_task_background(task_id: int, prompt: str, models_to_use: List[str], system_prompt: Optional[str] = None):
    """Background task to generate outputs. Creates its own DB session and manages streaming queues."""
    log.info(f"[Task BG {task_id}] Starting generation for prompt: {prompt[:50]} using models {models_to_use}...")
    
    # Ensure queues are created for all models before starting
    for model_id in models_to_use:
        await _get_or_create_model_queue(task_id, model_id)
        log.info(f"[Task BG {task_id}] Created queue for model {model_id}")

    async with AsyncSessionFactory() as db:
        try:
            # Fetch the task (still needed for status updates, prompt if not passed, etc.)
            # No longer need to fetch task just for use_blind_ids
            task = await crud_task.get_task(db, task_id=task_id)
            if not task:
                log.error(f"[Task BG {task_id}] Task not found, cannot proceed with generation.")
                # Signal error in all potential queues for this task if they were created
                task_queues_dict_on_error = await get_task_queues(task_id)
                for model_id_q_err, queue_err in task_queues_dict_on_error.items():
                    try:
                        await queue_err.put({"model": model_id_q_err, "status": "ERROR", "content": "Task setup failed: Task not found."})
                    except Exception:
                        pass # Ignore errors if queue is already closed etc.
                await remove_task_queues(task_id) # Clean up queues
                return
            
            await crud_task.update_task_status(db, task_id=task_id, status=TaskStatus.GENERATING)
            log.info(f"[Task BG {task_id}] Status set to GENERATING")

            client = await get_openai_client()
            # Start streaming generation tasks concurrently
            api_call_tasks = [
                generate_single_output_stream(client, task_id, model, prompt, system_prompt) 
                for model in models_to_use
            ]
            # Gather results (which now include full text/errors for DB storage)
            results = await asyncio.gather(*api_call_tasks, return_exceptions=True) 

            generation_records_data = []
            has_errors = False
            # Process results for DB storage after all streams are done/failed
            for model, result_or_exc in zip(models_to_use, results):
                # Always generate a blind_id, it's now non-nullable
                current_blind_id = uuid.uuid4().hex

                if isinstance(result_or_exc, Exception):
                    error_msg = f"Unhandled exception during generation task for {model}: {result_or_exc}"
                    log.error(error_msg)
                    # Ensure error marker is in queue if gather failed before stream completed
                    error_queue = await _get_or_create_model_queue(task_id, model)
                    await error_queue.put({"model": model, "status": "ERROR", "content": error_msg})
                    
                    generation_data = {
                        "model_id_used": model, 
                        "output_text": None, 
                        "reasoning_text": None,
                        "error_message": error_msg,
                        "blind_id": current_blind_id,
                        # Include usage statistics (set to None for errors)
                        "prompt_tokens": None,
                        "completion_tokens": None,
                        "total_tokens": None,
                        "reasoning_tokens": None,
                        "cached_tokens": None,
                        "cost_credits": None,
                        "generation_id": None,
                    }
                    generation_records_data.append(generation_data)
                    has_errors = True
                else: # result is a dict from generate_single_output_stream
                    generation_data = {
                        "model_id_used": model,
                        "output_text": result_or_exc["output_text"],
                        "reasoning_text": result_or_exc.get("reasoning_text"),
                        "error_message": result_or_exc["error_message"],
                        "blind_id": current_blind_id,
                        # Include usage statistics
                        "prompt_tokens": result_or_exc.get("prompt_tokens"),
                        "completion_tokens": result_or_exc.get("completion_tokens"),
                        "total_tokens": result_or_exc.get("total_tokens"),
                        "reasoning_tokens": result_or_exc.get("reasoning_tokens"),
                        "cached_tokens": result_or_exc.get("cached_tokens"),
                        "cost_credits": result_or_exc.get("cost_credits"),
                        "generation_id": result_or_exc.get("generation_id"),
                    }
                    generation_records_data.append(generation_data)
                    # Error message inside the result dict means the stream itself failed
                    if result_or_exc["error_message"]:
                        has_errors = True
            
            await crud_generation.bulk_create_generations(db, task_id=task_id, generations_data=generation_records_data)
            log.info(f"[Task BG {task_id}] Generation results saved to DB for models: {models_to_use}")

            final_status = TaskStatus.FAILED if has_errors else TaskStatus.COMPLETED
            await crud_task.update_task_status(db, task_id=task_id, status=final_status)
            log.info(f"[Task BG {task_id}] Generation complete. Final status set to {final_status}")

        except Exception as e:
            log.exception(f"[Task BG {task_id}] Unhandled error in generate_outputs_for_task_background")
            # Attempt to mark task as FAILED if an unexpected error occurs
            try:
                await crud_task.update_task_status(db, task_id=task_id, status=TaskStatus.FAILED)
            except Exception as db_e:
                log.error(f"[Task BG {task_id}] Failed to update task status to FAILED after unhandled error: {db_e}")
            # Signal error in all queues for this task
            task_queues_dict = await get_task_queues(task_id)
            for model_id, queue in task_queues_dict.items():
                 try:
                     await queue.put({"model": model_id, "status": "ERROR", "content": f"Task failed: {str(e)}"})
                 except Exception:
                      pass # Ignore errors if queue is already closed etc.
        finally:
            # Crucial: Remove the queues for this task_id to prevent memory leak
            # and signal closure to any remaining stream listeners.
            await remove_task_queues(task_id) 
            await db.close()


async def get_single_ranking(client: AsyncOpenAI, evaluator_model: str, task_prompt: str, generated_outputs: Dict[str, str], evaluation_use_blind_ids: bool, custom_evaluation_prompt: Optional[str] = None, system_prompt: Optional[str] = None) -> Dict[str, Any]:
    """Gets ranking and reasoning from a single evaluator model."""
    log.info(f"Getting ranking from evaluator: {evaluator_model}. Identifiers presented: {list(generated_outputs.keys())}. evaluation_use_blind_ids: {evaluation_use_blind_ids}")

    ids_presented_to_evaluator = list(generated_outputs.keys())

    outputs_formatted = "\\n\\n".join([
        f"--- Output from {output_identifier} ---\\n{text}"
        for output_identifier, text in generated_outputs.items()
    ])
    
    identifier_type_description = "Blind IDs" if evaluation_use_blind_ids else "Model Names/Identifiers"

    # Use custom evaluation prompt if provided, otherwise use default
    if custom_evaluation_prompt:
        # Custom prompt should include placeholders that we'll replace
        evaluation_instructions = custom_evaluation_prompt
        # Replace common placeholders in custom prompt
        evaluation_instructions = evaluation_instructions.replace("{task_prompt}", task_prompt)
        evaluation_instructions = evaluation_instructions.replace("{identifier_type}", identifier_type_description)
        evaluation_instructions = evaluation_instructions.replace("{identifiers}", str(ids_presented_to_evaluator))
        evaluation_instructions = evaluation_instructions.replace("{system_prompt}", system_prompt or "No system prompt was used")
        
        ranking_prompt = (
            f"Original Task Prompt:\\n{task_prompt}\\n\\n"
            f"System Prompt Used: {system_prompt or 'No system prompt was used'}\\n\\n"
            f"Generated Outputs to Rank (Identifiers are {identifier_type_description}: {ids_presented_to_evaluator}):\\n"
            f"{outputs_formatted}\\n\\n"
            f"---\\n"
            f"Instructions:\\n"
            f"{evaluation_instructions}\\n\\n"
            f"Provide your evaluation in the following JSON format ONLY:\\n"
            f"{{\\n"
            f"  \"ranking\": [\"identifier_best\", \"identifier_second\", ...], // A list of the provided Identifiers ({ids_presented_to_evaluator}), ordered from best to worst.\\n"
            f"  \"reasoning\": \"Your detailed explanation for the ranking order, comparing the outputs. Explain why you ranked them this way.\"\\n"
            f"}}\\n\\n"
            f"Ensure the output is valid JSON. The \"ranking\" list must contain all and only the Identifiers: {ids_presented_to_evaluator}."
        )
    else:
        # Default evaluation prompt
        ranking_prompt = (
            f"Original Task Prompt:\\n{task_prompt}\\n\\n"
            f"System Prompt Used: {system_prompt or 'No system prompt was used'}\\n\\n"
            f"Generated Outputs to Rank (Identifiers are {identifier_type_description}: {ids_presented_to_evaluator}):\\n"
            f"{outputs_formatted}\\n\\n"
            f"---\\n"
            f"Instructions:\\n"
            f"Evaluate the quality of the generated outputs based on the original task prompt. Consider relevance, completeness, coherence, and accuracy.\\n"
            f"Provide your evaluation in the following JSON format ONLY:\\n"
            f"{{\\n"
            f"  \"ranking\": [\"identifier_best\", \"identifier_second\", ...], // A list of the provided Identifiers ({ids_presented_to_evaluator}), ordered from best to worst.\\n"
            f"  \"reasoning\": \"Your detailed explanation for the ranking order, comparing the outputs. Explain why you ranked them this way.\"\\n"
            f"}}\\n\\n"
            f"Ensure the output is valid JSON. The \"ranking\" list must contain all and only the Identifiers: {ids_presented_to_evaluator}."
        )

    try:
        response = await client.chat.completions.create(
            model=evaluator_model,
            messages=[
                {"role": "system", "content": "You are an expert evaluator of language model outputs. Provide your response ONLY in the requested JSON format. Ensure the 'ranking' array in your JSON includes all provided model IDs/names and nothing else."},
                {"role": "user", "content": ranking_prompt}
            ],
            response_format={"type": "json_object"},
            temperature=0.2,
            timeout=180.0,
            extra_body={"usage": {"include": True}}  # Enable usage tracking for OpenRouter
        )
        content = response.choices[0].message.content
        log.info(f"Raw response from evaluator {evaluator_model}: {content[:300]}...")

        # Capture usage statistics from the response
        usage_stats = {}
        if hasattr(response, 'usage') and response.usage:
            usage_stats = {
                "prompt_tokens": getattr(response.usage, 'prompt_tokens', None),
                "completion_tokens": getattr(response.usage, 'completion_tokens', None),
                "total_tokens": getattr(response.usage, 'total_tokens', None),
                "reasoning_tokens": getattr(getattr(response.usage, 'completion_tokens_details', None), 'reasoning_tokens', None) if hasattr(response.usage, 'completion_tokens_details') else None,
                "cached_tokens": getattr(getattr(response.usage, 'prompt_tokens_details', None), 'cached_tokens', None) if hasattr(response.usage, 'prompt_tokens_details') else None,
                "cost_credits": getattr(response.usage, 'cost', None),
                "generation_id": getattr(response, 'id', None),
            }
            log.info(f"Usage stats captured for evaluator {evaluator_model}: {usage_stats}")

        parsed_result = json.loads(content)
        if "ranking" not in parsed_result or "reasoning" not in parsed_result:
            raise ValueError("JSON response missing 'ranking' or 'reasoning' key.")
        if not isinstance(parsed_result["ranking"], list) or not isinstance(parsed_result["reasoning"], str):
            raise ValueError("Invalid types for 'ranking' (list) or 'reasoning' (string).")

        ranked_ids_set = set(parsed_result["ranking"])
        original_ids_set = set(ids_presented_to_evaluator)
        if ranked_ids_set != original_ids_set:
            error_message = f"Evaluator {evaluator_model} returned an invalid ranking list. Expected identifiers: {original_ids_set}, Got: {ranked_ids_set}"
            log.error(error_message)
            return {"ranked_list_json": None, "reasoning_text": None, "error_message": error_message, **usage_stats}

        log.info(f"Successfully got structured ranking from {evaluator_model}")
        
        result = {
            "ranked_list_json": parsed_result["ranking"],
            "reasoning_text": parsed_result["reasoning"],
            "error_message": None
        }
        # Add usage statistics to the result
        result.update(usage_stats)
        return result
    except json.JSONDecodeError as e:
        response_content_for_log = content[:300] if 'content' in locals() else "Response content not available"
        error_msg = f"Error decoding JSON from {evaluator_model}: {e}. Response snippet: {response_content_for_log}..."
        log.error(error_msg)
        return {"ranked_list_json": None, "reasoning_text": None, "error_message": error_msg, **usage_stats}
    except Exception as e:
        error_msg = f"Error getting ranking from {evaluator_model}: {type(e).__name__} - {e}"
        log.exception(f"Error getting ranking from {evaluator_model}")
        return {"ranked_list_json": None, "reasoning_text": None, "error_message": error_msg, **usage_stats}

async def evaluate_outputs_for_task_background(task_id: int, evaluation_id: int, evaluator_models: List[str], evaluation_used_blind_ids: bool, custom_evaluation_prompt: Optional[str] = None):
    log.info(f"[Eval BG {evaluation_id} / Task {task_id}] Starting evaluation using models: {evaluator_models}. Evaluation will use_blind_ids: {evaluation_used_blind_ids}")
    async with AsyncSessionFactory() as db:
        try:
            await crud_evaluation.update_evaluation_status(db, evaluation_id=evaluation_id, status=TaskStatus.EVALUATING)

            # Fetch task for prompt (and potentially other task details if needed later)
            task = await crud_task.get_task(db, task_id=task_id)
            if not task:
                log.error(f"[Eval BG {evaluation_id}] Error: Task {task_id} not found for evaluation.")
                await crud_evaluation.update_evaluation_status(db, evaluation_id=evaluation_id, status=TaskStatus.FAILED)
                return
            task_prompt = task.prompt

            generations = await crud_generation.get_generations_for_task(db, task_id=task_id)
            if not generations:
                log.error(f"[Eval BG {evaluation_id}] Error: No generations found for task {task_id}.")
                await crud_evaluation.update_evaluation_status(db, evaluation_id=evaluation_id, status=TaskStatus.FAILED)
                return
            
            outputs_to_rank: Dict[str, str] = {}
            presented_identifier_to_actual_gen_id_map: Dict[str, int] = {}

            for gen in generations:
                if gen.output_text and not gen.error_message:
                    identifier_for_evaluator: str
                    # Use the evaluation-specific flag. gen.blind_id will always exist.
                    if evaluation_used_blind_ids: 
                        identifier_for_evaluator = gen.blind_id
                    else:
                        identifier_for_evaluator = gen.model_id_used 
                    
                    original_identifier = identifier_for_evaluator
                    counter = 1
                    while identifier_for_evaluator in outputs_to_rank: # Handle potential duplicates
                        identifier_for_evaluator = f"{original_identifier}_dup{counter}"
                        counter += 1
                    
                    outputs_to_rank[identifier_for_evaluator] = gen.output_text
                    presented_identifier_to_actual_gen_id_map[identifier_for_evaluator] = gen.id

            if not outputs_to_rank or len(outputs_to_rank) < 2:
                message = "Not enough valid outputs to perform ranking (need at least 2)."
                log.warning(f"[Eval BG {evaluation_id}] {message}")
                await crud_evaluation.update_evaluation_status(db, evaluation_id=evaluation_id, status=TaskStatus.FAILED)
                # Also update task status to FAILED when evaluation cannot proceed
                await crud_task.update_task_status(db, task_id=task_id, status=TaskStatus.FAILED)
                await crud_ranking.bulk_create_rankings(db, evaluation_id=evaluation_id, rankings_data=[
                    {"evaluator_model_id": model, "ranked_list_json": None, "reasoning_text": None, "error_message": message} 
                    for model in evaluator_models
                ])
                return
            
            log.info(f"[Eval BG {evaluation_id}] Fetched {len(outputs_to_rank)} outputs to rank. Identifiers for LLM: {list(outputs_to_rank.keys())}")

            client = await get_openai_client()
            api_call_tasks = [
                get_single_ranking(client, model_evaluator, task_prompt, outputs_to_rank, evaluation_used_blind_ids, custom_evaluation_prompt, task.system_prompt) # Pass evaluation_used_blind_ids, custom_evaluation_prompt and system_prompt
                for model_evaluator in evaluator_models
            ]
            results = await asyncio.gather(*api_call_tasks, return_exceptions=True)

            ranking_records_data = []
            successful_evaluations_count = 0 # Counter for successful evaluations

            for model_evaluator, result_or_exc in zip(evaluator_models, results):
                eval_record = {"evaluator_model_id": model_evaluator}
                is_current_eval_successful = False # Flag for this specific evaluator's success

                if isinstance(result_or_exc, Exception):
                    error_msg = f"Exception during ranking by {model_evaluator}: {result_or_exc}"
                    eval_record.update({
                        "ranked_list_json": None, 
                        "reasoning_text": None, 
                        "error_message": error_msg,
                        # Include usage statistics (set to None for errors)
                        "prompt_tokens": None,
                        "completion_tokens": None,
                        "total_tokens": None,
                        "reasoning_tokens": None,
                        "cached_tokens": None,
                        "cost_credits": None,
                        "generation_id": None,
                    })
                    # This evaluator failed, so is_current_eval_successful remains False
                else:
                    # result_or_exc is a dict from get_single_ranking
                    ranked_identifiers_from_evaluator = result_or_exc.get("ranked_list_json")
                    reasoning_text_from_evaluator = result_or_exc.get("reasoning_text")
                    error_from_evaluator = result_or_exc.get("error_message")

                    actual_generation_ids_for_ranking: List[int] = []
                    conversion_error_msg: Optional[str] = None

                    if ranked_identifiers_from_evaluator: # Only try to map if we have a list of identifiers
                        for presented_identifier in ranked_identifiers_from_evaluator:
                            actual_gen_id = presented_identifier_to_actual_gen_id_map.get(presented_identifier)
                            if actual_gen_id is not None:
                                actual_generation_ids_for_ranking.append(actual_gen_id)
                            else:
                                log.error(f"[Eval BG {evaluation_id}] Could not map presented identifier '{presented_identifier}' back to a generation ID. Evaluator: {model_evaluator}")
                                conversion_error_msg = f"Failed to map identifier '{presented_identifier}' from evaluator."
                                break # Stop mapping for this evaluator if an ID is not found
                    
                    combined_error_msg = error_from_evaluator or conversion_error_msg

                    eval_record.update({
                        "ranked_list_json": actual_generation_ids_for_ranking if not conversion_error_msg and ranked_identifiers_from_evaluator is not None else None,
                        "reasoning_text": reasoning_text_from_evaluator,
                        "error_message": combined_error_msg,
                        # Include usage statistics
                        "prompt_tokens": result_or_exc.get("prompt_tokens"),
                        "completion_tokens": result_or_exc.get("completion_tokens"),
                        "total_tokens": result_or_exc.get("total_tokens"),
                        "reasoning_tokens": result_or_exc.get("reasoning_tokens"),
                        "cached_tokens": result_or_exc.get("cached_tokens"),
                        "cost_credits": result_or_exc.get("cost_credits"),
                        "generation_id": result_or_exc.get("generation_id"),
                    })

                    if not combined_error_msg:
                        is_current_eval_successful = True
                
                ranking_records_data.append(eval_record)
                if is_current_eval_successful:
                    successful_evaluations_count += 1
            
            await crud_ranking.bulk_create_rankings(db, evaluation_id=evaluation_id, rankings_data=ranking_records_data)
            log.info(f"[Eval BG {evaluation_id}] Ranking results saved to DB from evaluators: {evaluator_models}. Successful evaluations: {successful_evaluations_count}")

            # Determine final evaluation status based on the count of successful evaluations
            if successful_evaluations_count >= 1:
                final_eval_status = TaskStatus.EVALUATION_DONE
                log.info(f"[Eval BG {evaluation_id}] Evaluation considered done with {successful_evaluations_count} valid ranking(s).")
            else:
                final_eval_status = TaskStatus.FAILED
                log.warning(f"[Eval BG {evaluation_id}] Evaluation failed. {successful_evaluations_count} valid rankings, need at least 1 to be considered done for aggregation purposes.")
            
            await crud_evaluation.update_evaluation_status(db, evaluation_id=evaluation_id, status=final_eval_status)
            
            # Update overall task status based on evaluation outcome
            if final_eval_status == TaskStatus.EVALUATION_DONE:
                await crud_task.update_task_status(db, task_id=task_id, status=TaskStatus.EVALUATION_DONE)
            elif final_eval_status == TaskStatus.FAILED: 
                task_current_status_obj = await crud_task.get_task(db, task_id=task_id) 
                if task_current_status_obj and task_current_status_obj.status != TaskStatus.FAILED :
                     await crud_task.update_task_status(db, task_id=task_id, status=TaskStatus.FAILED)

        except Exception as e:
            log.exception(f"[Eval BG {evaluation_id}] Unhandled error in evaluate_outputs_for_task_background")
            try:
                await crud_evaluation.update_evaluation_status(db, evaluation_id=evaluation_id, status=TaskStatus.FAILED)
                # Also ensure task is marked as failed if an unexpected error occurs during evaluation orchestration
                await crud_task.update_task_status(db, task_id=task_id, status=TaskStatus.FAILED)
            except Exception as db_e:
                log.error(f"[Eval BG {evaluation_id}] Failed to update statuses to FAILED after unhandled error: {db_e}")
        finally:
            await remove_task_queues(task_id) 
            await db.close()

async def get_generation_usage_by_id(generation_id: str) -> Optional[Dict[str, Any]]:
    """
    Retrieve usage statistics for a generation using OpenRouter's generation ID.
    This is useful for getting usage data asynchronously after generation completes.
    """
    try:
        client = await get_openai_client()
        
        # Use the httpx client directly to make the request to OpenRouter's generation endpoint
        async with client._client as http_client:
            response = await http_client.get(
                f"https://openrouter.ai/api/v1/generation/{generation_id}",
                headers={
                    "Authorization": f"Bearer {settings.OPENROUTER_API_KEY}",
                    "HTTP-Referer": "http://localhost",
                    "X-Title": "LLM Eval Platform"
                }
            )
            
            if response.status_code == 200:
                data = response.json()
                usage = data.get('usage', {})
                
                if usage:
                    return {
                        "prompt_tokens": usage.get('prompt_tokens'),
                        "completion_tokens": usage.get('completion_tokens'),
                        "total_tokens": usage.get('total_tokens'),
                        "reasoning_tokens": usage.get('completion_tokens_details', {}).get('reasoning_tokens'),
                        "cached_tokens": usage.get('prompt_tokens_details', {}).get('cached_tokens'),
                        "cost_credits": usage.get('cost'),
                    }
                else:
                    log.warning(f"No usage data found for generation {generation_id}")
                    return None
            else:
                log.error(f"Failed to retrieve generation {generation_id}: {response.status_code} - {response.text}")
                return None
                
    except Exception as e:
        log.error(f"Error retrieving usage for generation {generation_id}: {e}")
        return None

async def update_generation_usage_stats(generation_id: int, usage_stats: Dict[str, Any]) -> bool:
    """
    Update usage statistics for a generation record in the database.
    """
    try:
        async with AsyncSessionFactory() as db:
            generation = await crud_generation.get_generation_by_id(db, generation_id)
            if not generation:
                log.error(f"Generation {generation_id} not found for usage update")
                return False
            
            # Update the generation with usage statistics
            update_data = {
                "prompt_tokens": usage_stats.get("prompt_tokens"),
                "completion_tokens": usage_stats.get("completion_tokens"),
                "total_tokens": usage_stats.get("total_tokens"),
                "reasoning_tokens": usage_stats.get("reasoning_tokens"),
                "cached_tokens": usage_stats.get("cached_tokens"),
                "cost_credits": usage_stats.get("cost_credits"),
            }
            
            await crud_generation.update_generation_usage(db, generation_id, update_data)
            log.info(f"Updated usage statistics for generation {generation_id}")
            return True
            
    except Exception as e:
        log.error(f"Error updating usage statistics for generation {generation_id}: {e}")
        return False

