import logging
from typing import List, Dict, Optional, Tuple, Any
from collections import defaultdict
import scipy.stats

from app.schemas import task as task_schema
from app.crud import crud_evaluation, crud_task
from app.db.models import Ranking as RankingModel, Generation as GenerationModel
from sqlalchemy.ext.asyncio import AsyncSession

log = logging.getLogger(__name__)

class AggregationService:
    async def get_rankings_for_evaluation(self, db: AsyncSession, evaluation_id: int) -> List[RankingModel]:
        evaluation = await crud_evaluation.get_evaluation_with_rankings_task_and_generations(db, evaluation_id=evaluation_id)
        if not evaluation:
            raise ValueError(f"Evaluation with ID {evaluation_id} not found.")
        if not evaluation.rankings:
            raise ValueError(f"No rankings found for evaluation ID {evaluation_id}.")
        if not evaluation.task:
            raise ValueError(f"Task not found for evaluation ID {evaluation_id}.")
        if not evaluation.task.generations:
            log.warning(f"Task {evaluation.task_id} associated with evaluation {evaluation_id} has no generations.")
        return evaluation.rankings

    def _calculate_average_ranks(self, rankings_from_db: List[task_schema.Ranking]) -> Tuple[List[Dict[str, Any]], Dict[str, List[int]]]:
        model_scores: Dict[int, List[int]] = defaultdict(list)
        model_original_ranks_by_evaluator: Dict[int, Dict[str, int]] = defaultdict(lambda: defaultdict(int))
        evaluator_rankings_map: Dict[str, List[int]] = {}
        generated_model_ids_int = set()

        for db_ranking_entry in rankings_from_db:
            if db_ranking_entry.error_message or db_ranking_entry.ranked_list_json is None:
                log.warning(f"Skipping ranking from evaluator {db_ranking_entry.evaluator_model_id} for Pydantic-validated ranking data due to error or no data. Original ID if available: {getattr(db_ranking_entry, 'id', 'N/A')}")
                continue

            evaluator_id = db_ranking_entry.evaluator_model_id
            
            current_evaluator_ranked_list_int: List[int] = db_ranking_entry.ranked_list_json
            
            evaluator_rankings_map[evaluator_id] = current_evaluator_ranked_list_int
            
            for rank, gen_id in enumerate(current_evaluator_ranked_list_int):
                generated_model_ids_int.add(gen_id)
                model_scores[gen_id].append(rank + 1)
                model_original_ranks_by_evaluator[gen_id][evaluator_id] = rank + 1

        if not generated_model_ids_int:
             raise ValueError("No valid rankings found to aggregate after Pydantic validation and filtering.")

        aggregated_results_intermediate: List[Dict[str, Any]] = []
        for gen_id in generated_model_ids_int:
            scores = model_scores.get(gen_id, [])
            if not scores:
                avg_score = float('inf')
            else:
                avg_score = sum(scores) / len(scores)
            
            aggregated_results_intermediate.append({
                "model_id_used": gen_id,
                "aggregated_score": avg_score,
                "original_ranks": model_original_ranks_by_evaluator.get(gen_id, {})
            })

        aggregated_results_intermediate.sort(key=lambda x: x["aggregated_score"])
        
        return aggregated_results_intermediate, evaluator_rankings_map

    def _calculate_borda_count(self, rankings_from_db: List[task_schema.Ranking]) -> Tuple[List[Dict[str, Any]], Dict[str, List[int]]]:
        model_scores: Dict[int, int] = defaultdict(int)
        model_original_ranks_by_evaluator: Dict[int, Dict[str, int]] = defaultdict(lambda: defaultdict(int))
        evaluator_rankings_map: Dict[str, List[int]] = {}
        generated_model_ids_int = set()
        
        num_models_to_rank = 0
        temp_parsed_lists_for_borda: Dict[str, List[int]] = {}

        for db_ranking_entry in rankings_from_db:
            if db_ranking_entry.error_message or db_ranking_entry.ranked_list_json is None:
                log.warning(f"Borda: Skipping Pydantic-validated ranking data for {db_ranking_entry.evaluator_model_id}. Original ID: {getattr(db_ranking_entry, 'id', 'N/A')}")
                continue

            evaluator_id = db_ranking_entry.evaluator_model_id
            current_evaluator_ranked_list_int_borda: List[int] = db_ranking_entry.ranked_list_json
            
            if current_evaluator_ranked_list_int_borda:
                temp_parsed_lists_for_borda[evaluator_id] = current_evaluator_ranked_list_int_borda
                num_models_to_rank = max(num_models_to_rank, len(current_evaluator_ranked_list_int_borda))
                for gen_id in current_evaluator_ranked_list_int_borda:
                    generated_model_ids_int.add(gen_id)

        if not generated_model_ids_int:
            raise ValueError("No valid rankings found to aggregate for Borda count after Pydantic validation.")

        for evaluator_id, current_evaluator_ranked_list_int in temp_parsed_lists_for_borda.items():
            evaluator_rankings_map[evaluator_id] = current_evaluator_ranked_list_int
            
            for rank, gen_id in enumerate(current_evaluator_ranked_list_int):
                points = num_models_to_rank - (rank + 1)
                model_scores[gen_id] += points
                model_original_ranks_by_evaluator[gen_id][evaluator_id] = rank + 1
        
        aggregated_results_intermediate: List[Dict[str, Any]] = []
        for gen_id in generated_model_ids_int: 
            aggregated_results_intermediate.append({
                "model_id_used": gen_id,
                "aggregated_score": model_scores.get(gen_id, 0),
                "original_ranks": model_original_ranks_by_evaluator.get(gen_id, {})
            })

        aggregated_results_intermediate.sort(key=lambda x: x["aggregated_score"], reverse=True)

        return aggregated_results_intermediate, evaluator_rankings_map

    def _calculate_weighted_average_ranks(self, rankings_from_db: List[task_schema.Ranking], evaluator_weights: Optional[Dict[str, float]] = None) -> Tuple[List[Dict[str, Any]], Dict[str, List[int]]]:
        log.info(f"Calculating weighted average ranks. Provided weights: {evaluator_weights}")
        model_weighted_scores: Dict[int, float] = defaultdict(float)
        model_total_weights: Dict[int, float] = defaultdict(float)
        model_original_ranks_by_evaluator: Dict[int, Dict[str, int]] = defaultdict(lambda: defaultdict(int))
        evaluator_rankings_map: Dict[str, List[int]] = {}
        generated_model_ids_int = set()

        for db_ranking_entry in rankings_from_db:
            if db_ranking_entry.error_message or db_ranking_entry.ranked_list_json is None:
                log.warning(f"Weighted: Skipping Pydantic-validated ranking for {db_ranking_entry.evaluator_model_id}. Original ID: {getattr(db_ranking_entry, 'id', 'N/A')}")
                continue

            evaluator_id = db_ranking_entry.evaluator_model_id
            current_evaluator_ranked_list_int_weighted: List[int] = db_ranking_entry.ranked_list_json

            evaluator_rankings_map[evaluator_id] = current_evaluator_ranked_list_int_weighted
            
            weight = evaluator_weights.get(evaluator_id, 1.0) if evaluator_weights else 1.0
            if weight <= 0:
                log.warning(f"Skipping evaluator {evaluator_id} due to non-positive weight: {weight}")
                continue

            for rank, gen_id in enumerate(current_evaluator_ranked_list_int_weighted):
                generated_model_ids_int.add(gen_id)
                model_weighted_scores[gen_id] += (rank + 1) * weight
                model_total_weights[gen_id] += weight
                model_original_ranks_by_evaluator[gen_id][evaluator_id] = rank + 1

        if not generated_model_ids_int:
            raise ValueError("No valid rankings found to aggregate for weighted average after Pydantic validation.")

        aggregated_results_intermediate: List[Dict[str, Any]] = []
        for gen_id in generated_model_ids_int:
            if model_total_weights.get(gen_id, 0) > 0:
                avg_score = model_weighted_scores[gen_id] / model_total_weights[gen_id]
            else:
                avg_score = float('inf')
            
            aggregated_results_intermediate.append({
                "model_id_used": gen_id,
                "aggregated_score": avg_score,
                "original_ranks": model_original_ranks_by_evaluator.get(gen_id, {})
            })

        aggregated_results_intermediate.sort(key=lambda x: x["aggregated_score"])

        return aggregated_results_intermediate, evaluator_rankings_map

    def _calculate_copeland_method(self, rankings_from_db: List[task_schema.Ranking]) -> Tuple[List[Dict[str, Any]], Dict[str, List[int]]]:
        log.info("Calculating Copeland scores.")
        model_pairwise_scores: Dict[int, int] = defaultdict(int)
        model_original_ranks_by_evaluator: Dict[int, Dict[str, int]] = defaultdict(lambda: defaultdict(int))
        evaluator_rankings_map: Dict[str, List[int]] = {}
        all_generation_ids_int = set()

        for db_ranking_entry in rankings_from_db:
            if db_ranking_entry.error_message or db_ranking_entry.ranked_list_json is None:
                log.warning(f"Copeland: Skipping Pydantic-validated ranking for {db_ranking_entry.evaluator_model_id}. Original ID: {getattr(db_ranking_entry, 'id', 'N/A')}")
                continue
            
            evaluator_id = db_ranking_entry.evaluator_model_id
            current_evaluator_ranked_list_int_copeland: List[int] = db_ranking_entry.ranked_list_json

            evaluator_rankings_map[evaluator_id] = current_evaluator_ranked_list_int_copeland
            
            for rank, gen_id in enumerate(current_evaluator_ranked_list_int_copeland):
                all_generation_ids_int.add(gen_id)
                model_original_ranks_by_evaluator[gen_id][evaluator_id] = rank + 1

        if not all_generation_ids_int:
            raise ValueError("No valid models found to perform Copeland method after Pydantic validation.")

        sorted_generation_ids_int = sorted(list(all_generation_ids_int))

        for i in range(len(sorted_generation_ids_int)):
            for j in range(i + 1, len(sorted_generation_ids_int)):
                gen_id_a = sorted_generation_ids_int[i]
                gen_id_b = sorted_generation_ids_int[j]
                
                wins_a = 0
                wins_b = 0
                
                for evaluator_id, ranked_list_int in evaluator_rankings_map.items():
                    try:
                        rank_a = ranked_list_int.index(gen_id_a)
                        rank_b = ranked_list_int.index(gen_id_b)
                        if rank_a < rank_b:
                            wins_a += 1
                        elif rank_b < rank_a:
                            wins_b += 1
                    except ValueError:
                        pass 
                
                if wins_a > wins_b:
                    model_pairwise_scores[gen_id_a] += 1
                    model_pairwise_scores[gen_id_b] -= 1
                elif wins_b > wins_a:
                    model_pairwise_scores[gen_id_b] += 1
                    model_pairwise_scores[gen_id_a] -= 1

        aggregated_results_intermediate: List[Dict[str, Any]] = []
        for gen_id in sorted_generation_ids_int:
            aggregated_results_intermediate.append({
                "model_id_used": gen_id,
                "aggregated_score": model_pairwise_scores.get(gen_id, 0),
                "original_ranks": model_original_ranks_by_evaluator.get(gen_id, {})
            })
        
        aggregated_results_intermediate.sort(key=lambda x: x["aggregated_score"], reverse=True)

        return aggregated_results_intermediate, evaluator_rankings_map

    def _calculate_kendall_tau(self, list1: List[str], list2: List[str]) -> Optional[float]:
        if not list1 or not list2:
            log.warning("Kendall Tau: One or both lists are empty.")
            return None
        
        if len(list1) != len(list2):
            log.warning(f"Kendall Tau: Lists have different lengths. List1: {len(list1)}, List2: {len(list2)}. Cannot compute fairly.")
            return None
            
        if not list1:
            return None

        try:
            tau, p_value = scipy.stats.kendalltau(list1, list2, variant='b')
            return tau
        except Exception as e:
            log.error(f"Error calculating Kendall Tau. List1 (len {len(list1)}): {list1[:20]}... List2 (len {len(list2)}): {list2[:20]}... Error: {e}")
            return None

    def _calculate_consistencies(
        self,
        final_aggregated_items: List[task_schema.AggregatedRankingItem],
        evaluator_rankings_map: Dict[str, List[int]],
        generation_id_to_model_name_map: Dict[int, str]
    ) -> List[task_schema.EvaluatorConsistency]:
        
        consistencies: List[task_schema.EvaluatorConsistency] = []
        if not final_aggregated_items:
            return []

        aggregated_order_str: List[str] = [item.model_id_used for item in final_aggregated_items]

        for evaluator_id, evaluator_ordered_gen_ids_list_int in evaluator_rankings_map.items():
            error_msg = None
            tau = None
            try:
                evaluator_ordered_model_names_str: List[str] = []
                for gen_id in evaluator_ordered_gen_ids_list_int:
                    model_name = generation_id_to_model_name_map.get(gen_id)
                    if model_name:
                        evaluator_ordered_model_names_str.append(model_name)
                    else:
                        log.warning(f"Consistency: Gen ID {gen_id} from evaluator {evaluator_id} not in map. Skipping.")
                
                if not evaluator_ordered_model_names_str:
                    error_msg = "Evaluator list empty after mapping or all gen_ids were unmappable."
                    tau = None
                else:
                    common_models_str = set(aggregated_order_str) & set(evaluator_ordered_model_names_str)
                    
                    if len(common_models_str) < 2: 
                        tau = None 
                        error_msg = "Not enough common models (string identifiers) to compare for Kendall Tau."
                    else:
                        agg_common_ordered_str = [m_str for m_str in aggregated_order_str if m_str in common_models_str]
                        eval_common_ordered_str = [m_str for m_str in evaluator_ordered_model_names_str if m_str in common_models_str]
                        
                        tau = self._calculate_kendall_tau(agg_common_ordered_str, eval_common_ordered_str)
                        if tau is None and not error_msg: 
                            error_msg = "Kendall Tau calculation failed or lists were incompatible after mapping."

            except Exception as e:
                log.error(f"Failed to calculate consistency for {evaluator_id}: {e}")
                error_msg = str(e)
            
            consistencies.append(
                task_schema.EvaluatorConsistency(
                    evaluator_model_id=evaluator_id,
                    consistency_score=tau if tau is not None else None, 
                    error_message=error_msg
                )
            )
        return consistencies
    
    def _calculate_overall_consistency(self, consistencies: List[task_schema.EvaluatorConsistency]) -> Optional[float]:
        valid_scores = [c.consistency_score for c in consistencies if c.consistency_score is not None and c.error_message is None]
        if not valid_scores:
            return None
        return sum(valid_scores) / len(valid_scores)

    async def aggregate_evaluation_results(
        self,
        db: AsyncSession,
        evaluation_id: int,
        algorithm: task_schema.AggregationAlgorithmEnum,
        evaluator_weights: Optional[Dict[str, float]] = None
    ) -> task_schema.EvaluationAggregationResponse:
        log.info(f"Aggregating results for evaluation ID {evaluation_id} using algorithm {algorithm.value}")
        
        evaluation_db_model = await crud_evaluation.get_evaluation_with_rankings_task_and_generations(db, evaluation_id=evaluation_id)

        if not evaluation_db_model:
             raise ValueError(f"Evaluation {evaluation_id} not found.")
        if not evaluation_db_model.task:
            raise ValueError(f"Task for evaluation {evaluation_id} not found.")
        if not evaluation_db_model.task.generations:
             log.warning(f"Task {evaluation_db_model.task_id} has no generations. Cannot map generation IDs to model names.")
             return task_schema.EvaluationAggregationResponse(
                evaluation_id=evaluation_id,
                task_id=evaluation_db_model.task_id,
                algorithm_used=algorithm,
                error_message="Task has no generation data, cannot perform aggregation."
            )

        task_id = evaluation_db_model.task_id
        generations_for_task: List[GenerationModel] = evaluation_db_model.task.generations
        
        generation_id_to_model_name_map: Dict[int, str] = {
            gen.id: gen.model_id_used for gen in generations_for_task if gen.id is not None and gen.model_id_used is not None
        }
        if not generation_id_to_model_name_map and any(r.ranked_list_json for r in evaluation_db_model.rankings or []):
            log.error(f"Failed to create generation_id_to_model_name_map for task {task_id} but rankings exist. Generations: {generations_for_task}")
            return task_schema.EvaluationAggregationResponse(
                evaluation_id=evaluation_id, task_id=task_id, algorithm_used=algorithm,
                error_message="Internal error: Could not map generation IDs to model names, though rankings are present."
            )
        
        try:
            rankings_orm: List[RankingModel] = await self.get_rankings_for_evaluation(db, evaluation_id)
            
            rankings_pydantic: List[task_schema.Ranking] = []
            for orm_ranking in rankings_orm:
                try:
                    pydantic_ranking = task_schema.Ranking.model_validate(orm_ranking)
                    if pydantic_ranking.ranked_list_json is not None:
                         rankings_pydantic.append(pydantic_ranking)
                    elif not pydantic_ranking.error_message :
                        log.info(f"ORM ranking ID {orm_ranking.id} for evaluator {orm_ranking.evaluator_model_id} has None for ranked_list_json and no error message. Will be skipped by calculation methods.")
                except Exception as e:
                    log.error(f"Failed to validate ORM ranking ID {orm_ranking.id} (evaluator: {orm_ranking.evaluator_model_id}) to Pydantic schema: {e}. Skipping this ranking.")
                    continue 

            if not rankings_pydantic and rankings_orm :
                log.error(f"All {len(rankings_orm)} ORM rankings for eval {evaluation_id} failed Pydantic validation. No data to aggregate.")
                return task_schema.EvaluationAggregationResponse(
                    evaluation_id=evaluation_id, task_id=task_id, algorithm_used=algorithm,
                    error_message="All ranking data failed validation. Cannot perform aggregation."
                )
            
            intermediate_aggregated_results: List[Dict[str, Any]]
            evaluator_rankings_map_gen_ids: Dict[str, List[int]]

            if algorithm == task_schema.AggregationAlgorithmEnum.AVERAGE_RANK:
                intermediate_aggregated_results, evaluator_rankings_map_gen_ids = self._calculate_average_ranks(rankings_pydantic)
            elif algorithm == task_schema.AggregationAlgorithmEnum.BORDA_COUNT:
                intermediate_aggregated_results, evaluator_rankings_map_gen_ids = self._calculate_borda_count(rankings_pydantic)
            elif algorithm == task_schema.AggregationAlgorithmEnum.WEIGHTED_AVERAGE_RANK:
                intermediate_aggregated_results, evaluator_rankings_map_gen_ids = self._calculate_weighted_average_ranks(rankings_pydantic, evaluator_weights)
            elif algorithm == task_schema.AggregationAlgorithmEnum.COPELAND_METHOD:
                intermediate_aggregated_results, evaluator_rankings_map_gen_ids = self._calculate_copeland_method(rankings_pydantic)
            else:
                raise NotImplementedError(f"Aggregation algorithm {algorithm.value} not implemented.")

            if not intermediate_aggregated_results:
                 return task_schema.EvaluationAggregationResponse(
                    evaluation_id=evaluation_id, task_id=task_id, algorithm_used=algorithm,
                    aggregated_rankings=[], evaluator_consistencies=[],
                    error_message="No aggregated items could be produced by the calculation method (post-validation)."
                )

            final_aggregated_items: List[task_schema.AggregatedRankingItem] = []
            for i, item_dict in enumerate(intermediate_aggregated_results):
                gen_id = item_dict.get("model_id_used")
                model_name_str = generation_id_to_model_name_map.get(gen_id)

                if gen_id is None or model_name_str is None:
                    log.error(f"Failed to map generation ID {gen_id} to model name. Item: {item_dict}")
                    continue 
                
                final_aggregated_items.append(
                    task_schema.AggregatedRankingItem(
                        model_id_used=model_name_str,
                        aggregated_score=item_dict["aggregated_score"],
                        original_ranks=item_dict["original_ranks"],
                        rank=i + 1
                    )
                )
            
            final_aggregated_items.sort(key=lambda x: x.aggregated_score if algorithm != task_schema.AggregationAlgorithmEnum.BORDA_COUNT and algorithm != task_schema.AggregationAlgorithmEnum.COPELAND_METHOD else -x.aggregated_score)
            for i, item_model in enumerate(final_aggregated_items):
                item_model.rank = i + 1


            if not final_aggregated_items and intermediate_aggregated_results :
                 return task_schema.EvaluationAggregationResponse(
                    evaluation_id=evaluation_id, task_id=task_id, algorithm_used=algorithm,
                    aggregated_rankings=[], evaluator_consistencies=[],
                    error_message="Critical error: All aggregated items failed model name mapping (post-validation)."
                )
            consistencies = self._calculate_consistencies(
                final_aggregated_items, 
                evaluator_rankings_map_gen_ids, 
                generation_id_to_model_name_map
            )
            overall_consistency = self._calculate_overall_consistency(consistencies)

            return task_schema.EvaluationAggregationResponse(
                evaluation_id=evaluation_id,
                task_id=task_id,
                algorithm_used=algorithm,
                aggregated_rankings=final_aggregated_items,
                evaluator_consistencies=consistencies,
                overall_consistency=overall_consistency
            )
        except ValueError as ve: 
            log.error(f"ValueError during aggregation for eval {evaluation_id}: {ve}")
            return task_schema.EvaluationAggregationResponse(
                evaluation_id=evaluation_id,
                task_id=task_id, 
                algorithm_used=algorithm,
                error_message=str(ve)
            )
        except Exception as e:
            log.exception(f"Failed to aggregate results for evaluation {evaluation_id}: {e}")
            return task_schema.EvaluationAggregationResponse(
                evaluation_id=evaluation_id,
                task_id=task_id, 
                algorithm_used=algorithm,
                error_message=f"An unexpected error occurred: {str(e)}"
            )

aggregation_service = AggregationService() 