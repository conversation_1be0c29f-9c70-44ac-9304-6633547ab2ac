from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import selectinload
from typing import Optional, List
from sqlalchemy import delete
import logging

from app.db import models
from app.schemas import task as task_schema

# Define the logger for this module
log = logging.getLogger(__name__)

async def create_task(db: AsyncSession, task_in: task_schema.TaskCreate) -> models.Task:
    """
    Creates a new task record in the database.
    """
    db_task = models.Task(
        prompt=task_in.prompt,
        system_prompt=task_in.system_prompt,
        status=task_schema.TaskStatusEnum.PENDING
    )
    db.add(db_task)
    await db.commit()
    await db.refresh(db_task)
    return db_task

async def get_task(db: AsyncSession, task_id: int) -> Optional[models.Task]:
    """
    Retrieves a single task by ID.
    """
    result = await db.execute(select(models.Task).filter(models.Task.id == task_id))
    return result.scalars().first()

async def get_task_with_generations(db: AsyncSession, task_id: int) -> Optional[models.Task]:
    """
    Retrieves a task and eagerly loads its associated generations.
    """
    result = await db.execute(
        select(models.Task)
        .options(selectinload(models.Task.generations))
        .filter(models.Task.id == task_id)
    )
    return result.scalars().first()

async def get_task_with_evaluations(db: AsyncSession, task_id: int) -> Optional[models.Task]:
    """
    Retrieves a task and eagerly loads its associated evaluations and rankings.
    """
    result = await db.execute(
        select(models.Task)
        .options(selectinload(models.Task.evaluations).selectinload(models.Evaluation.rankings))
        .filter(models.Task.id == task_id)
    )
    return result.scalars().first()

async def get_task_with_details(db: AsyncSession, task_id: int) -> Optional[models.Task]:
    """
    Retrieves a task and eagerly loads its associated generations and evaluations (with rankings).
    """
    result = await db.execute(
        select(models.Task)
        .options(
            selectinload(models.Task.generations),
            selectinload(models.Task.evaluations).selectinload(models.Evaluation.rankings)
        )
        .filter(models.Task.id == task_id)
    )
    return result.scalars().first()

async def get_tasks_for_history(db: AsyncSession, skip: int = 0, limit: int = 50) -> List[models.Task]:
    """Retrieve tasks with minimal info for history display, newest first."""
    result = await db.execute(
        select(models.Task.id, models.Task.prompt, models.Task.status, models.Task.created_at)
        .order_by(models.Task.created_at.desc())
        .offset(skip)
        .limit(limit)
    )
    return result.all()

async def update_task_status(db: AsyncSession, task_id: int, status: task_schema.TaskStatusEnum) -> Optional[models.Task]:
    """
    Updates the status of a task.
    """
    db_task = await get_task(db=db, task_id=task_id)
    if db_task:
        db_task.status = status
        await db.commit()
        await db.refresh(db_task)
    return db_task

async def delete_task_recursive(db: AsyncSession, task_id: int) -> bool:
    """Deletes a task and all its associated data."""
    # Use selectinload to fetch the task with related evaluations and their rankings
    result = await db.execute(
        select(models.Task)
        .options(
            selectinload(models.Task.evaluations).selectinload(models.Evaluation.rankings),
            selectinload(models.Task.generations) # Also load generations directly
        )
        .where(models.Task.id == task_id)
    )
    db_task = result.scalar_one_or_none()

    if not db_task:
        return False # Task not found

    log.info(f"Starting deletion for Task ID: {task_id}")

    # 1. Delete Rankings associated with Evaluations of this Task
    evaluation_ids = [ev.id for ev in db_task.evaluations]
    if evaluation_ids:
        log.info(f"Deleting Rankings for Evaluation IDs: {evaluation_ids}")
        await db.execute(
            delete(models.Ranking).where(models.Ranking.evaluation_id.in_(evaluation_ids))
        )

    # 2. Delete Evaluations associated with this Task
    if db_task.evaluations:
        log.info(f"Deleting {len(db_task.evaluations)} Evaluations for Task ID: {task_id}")
        # Because we loaded evaluations, we can delete them directly
        for evaluation in db_task.evaluations:
             await db.delete(evaluation)
        # Or alternatively, delete by task_id if not preloaded:
        # await db.execute(delete(models.Evaluation).where(models.Evaluation.task_id == task_id))


    # 3. Delete Generations associated with this Task
    if db_task.generations:
        log.info(f"Deleting {len(db_task.generations)} Generations for Task ID: {task_id}")
        # Because we loaded generations, we can delete them directly
        for generation in db_task.generations:
            await db.delete(generation)
        # Or alternatively, delete by task_id if not preloaded:
        # await db.execute(delete(models.Generation).where(models.Generation.task_id == task_id))


    # 4. Delete the Task itself
    log.info(f"Deleting Task ID: {task_id}")
    await db.delete(db_task)

    await db.commit()
    log.info(f"Successfully deleted Task ID: {task_id} and associated data.")
    return True

# Placeholder for other task-related CRUD functions if needed 