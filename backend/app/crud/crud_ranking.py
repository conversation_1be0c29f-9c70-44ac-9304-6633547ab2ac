from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update
from typing import List, Dict, Any, Optional

from app.db import models
from app.schemas import task as task_schema # Keeping schema import consistent

async def create_ranking(db: AsyncSession, ranking_in: task_schema.RankingCreate) -> models.Ranking:
    """
    Creates a single ranking record.
    """
    # Note: Pydantic v2 uses model_dump() instead of dict()
    db_ranking = models.Ranking(**ranking_in.model_dump())
    db.add(db_ranking)
    await db.commit()
    await db.refresh(db_ranking)
    return db_ranking

async def bulk_create_rankings(db: AsyncSession, evaluation_id: int, rankings_data: List[Dict[str, Any]]) -> List[models.Ranking]:
    """
    Efficiently creates multiple ranking records for a specific evaluation.
    Expects a list of dictionaries, each with ranking details and usage statistics.
    """
    db_rankings = [
        models.Ranking(
            evaluation_id=evaluation_id,
            evaluator_model_id=rank_data["evaluator_model_id"],
            ranked_list_json=rank_data.get("ranked_list_json"),
            reasoning_text=rank_data.get("reasoning_text"),
            error_message=rank_data.get("error_message"),
            # Include usage statistics fields
            prompt_tokens=rank_data.get("prompt_tokens"),
            completion_tokens=rank_data.get("completion_tokens"),
            total_tokens=rank_data.get("total_tokens"),
            reasoning_tokens=rank_data.get("reasoning_tokens"),
            cached_tokens=rank_data.get("cached_tokens"),
            cost_credits=rank_data.get("cost_credits"),
            generation_id=rank_data.get("generation_id"),
        ) for rank_data in rankings_data
    ]
    db.add_all(db_rankings)
    await db.commit()
    # Similar to bulk_create_generations, refresh is not automatic.
    return db_rankings # Return list of objects added 

async def get_ranking_by_id(db: AsyncSession, ranking_id: int) -> Optional[models.Ranking]:
    """
    Retrieve a ranking by its ID.
    """
    result = await db.execute(select(models.Ranking).filter(models.Ranking.id == ranking_id))
    return result.scalars().first()

async def update_ranking_usage(db: AsyncSession, ranking_id: int, usage_data: Dict[str, Any]) -> Optional[models.Ranking]:
    """
    Update usage statistics for a ranking record.
    """
    result = await db.execute(
        update(models.Ranking)
        .where(models.Ranking.id == ranking_id)
        .values(**usage_data)
        .returning(models.Ranking)
    )
    await db.commit()
    return result.scalars().first() 