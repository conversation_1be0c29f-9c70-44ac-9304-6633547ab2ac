from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import selectinload
from typing import Optional

from app.db import models
from app.schemas import task as task_schema # Keeping schema import consistent

async def create_evaluation(db: AsyncSession, task_id: int, evaluation_used_blind_ids: bool, evaluation_prompt: Optional[str] = None) -> models.Evaluation:
    """
    Creates an evaluation record linked to a task.
    Initial status is PENDING.
    Sets whether blind IDs were used for this evaluation.
    Stores the evaluation prompt that was used.
    """
    db_evaluation = models.Evaluation(
        task_id=task_id, 
        status=task_schema.TaskStatusEnum.PENDING,
        evaluation_used_blind_ids=evaluation_used_blind_ids,
        evaluation_prompt=evaluation_prompt
    )
    db.add(db_evaluation)
    await db.commit()
    await db.refresh(db_evaluation)
    return db_evaluation

async def get_evaluation(db: AsyncSession, evaluation_id: int) -> Optional[models.Evaluation]:
    """
    Retrieves a single evaluation by ID.
    """
    result = await db.execute(select(models.Evaluation).filter(models.Evaluation.id == evaluation_id))
    return result.scalars().first()

async def get_evaluation_with_rankings(db: AsyncSession, evaluation_id: int) -> Optional[models.Evaluation]:
    """
    Retrieves an evaluation and eagerly loads its associated rankings.
    """
    result = await db.execute(
        select(models.Evaluation)
        .options(selectinload(models.Evaluation.rankings))
        .filter(models.Evaluation.id == evaluation_id)
    )
    return result.scalars().first()

async def get_evaluation_with_rankings_task_and_generations(db: AsyncSession, evaluation_id: int) -> Optional[models.Evaluation]:
    """
    Retrieves an evaluation and eagerly loads its associated task, the task's generations, and the evaluation's rankings.
    """
    result = await db.execute(
        select(models.Evaluation)
        .options(
            selectinload(models.Evaluation.task).selectinload(models.Task.generations),
            selectinload(models.Evaluation.rankings)
        )
        .filter(models.Evaluation.id == evaluation_id)
    )
    return result.scalars().first()

async def update_evaluation_status(db: AsyncSession, evaluation_id: int, status: task_schema.TaskStatusEnum) -> Optional[models.Evaluation]:
    """
    Updates the status of an evaluation.
    """
    db_evaluation = await get_evaluation(db=db, evaluation_id=evaluation_id)
    if db_evaluation:
        db_evaluation.status = status
        await db.commit()
        await db.refresh(db_evaluation)
    return db_evaluation 