from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from typing import List, Dict, Any, Optional

from app.db import models
from app.schemas import task as task_schema # Keeping schema import consistent

async def create_generation(db: AsyncSession, generation_in: task_schema.GenerationCreate) -> models.Generation:
    """
    Creates a single generation record.
    """
    db_generation = models.Generation(**generation_in.model_dump())
    db.add(db_generation)
    await db.commit()
    await db.refresh(db_generation)
    return db_generation

async def bulk_create_generations(db: AsyncSession, task_id: int, generations_data: List[Dict[str, Any]]) -> List[models.Generation]:
    """
    Efficiently creates multiple generation records for a specific task.
    Expects a list of dictionaries, each containing model_id_used, output_text, error_message, blind_id, and usage statistics.
    """
    db_generations = [
        models.Generation(
            task_id=task_id,
            model_id_used=gen_data["model_id_used"],
            output_text=gen_data.get("output_text"),
            reasoning_text=gen_data.get("reasoning_text"),
            error_message=gen_data.get("error_message"),
            blind_id=gen_data["blind_id"],
            # Usage statistics fields
            prompt_tokens=gen_data.get("prompt_tokens"),
            completion_tokens=gen_data.get("completion_tokens"),
            total_tokens=gen_data.get("total_tokens"),
            reasoning_tokens=gen_data.get("reasoning_tokens"),
            cached_tokens=gen_data.get("cached_tokens"),
            cost_credits=gen_data.get("cost_credits"),
            generation_id=gen_data.get("generation_id"),
        ) for gen_data in generations_data
    ]
    db.add_all(db_generations)
    await db.commit()
    # Note: bulk operations don't automatically refresh objects with IDs like single add.
    # We fetch them again if needed, or assume they are created.
    # For simplicity here, we won't refresh them after bulk insert.
    # If IDs are needed immediately, consider adding them individually or fetching after commit.
    return db_generations # Return the list of objects added (without refreshed state)

async def get_generations_for_task(db: AsyncSession, task_id: int) -> List[models.Generation]:
    """
    Retrieves all generations associated with a specific task ID.
    """
    result = await db.execute(
        select(models.Generation).filter(models.Generation.task_id == task_id)
    )
    return result.scalars().all() 

async def get_generation_by_id(db: AsyncSession, generation_id: int) -> Optional[models.Generation]:
    """
    Retrieves a single generation by its ID.
    """
    result = await db.execute(
        select(models.Generation).filter(models.Generation.id == generation_id)
    )
    return result.scalars().first()

async def update_generation_usage(db: AsyncSession, generation_id: int, usage_data: Dict[str, Any]) -> bool:
    """
    Updates usage statistics for a generation.
    """
    try:
        generation = await get_generation_by_id(db, generation_id)
        if not generation:
            return False
        
        # Update the usage fields
        for field, value in usage_data.items():
            if hasattr(generation, field) and value is not None:
                setattr(generation, field, value)
        
        await db.commit()
        return True
    except Exception:
        await db.rollback()
        return False 