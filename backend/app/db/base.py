from typing import AsyncGenerator
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.orm import DeclarativeBase
from app.core.config import settings

# Base class for ORM models
class Base(DeclarativeBase):
    pass

# Lazy engine and session factory
_engine = None
_SessionFactory = None

def get_engine():
    """Get or create the async engine"""
    global _engine
    if _engine is None:
        _engine = create_async_engine(
            settings.DATABASE_URL,
            pool_pre_ping=True,
            connect_args={"check_same_thread": False} if "sqlite" in settings.DATABASE_URL else {}
        )
    return _engine

def get_session_factory():
    """Get or create the async session factory"""
    global _SessionFactory
    if _SessionFactory is None:
        _SessionFactory = async_sessionmaker(
            bind=get_engine(),
            autocommit=False,
            autoflush=False,
            expire_on_commit=False,
            class_=AsyncSession
        )
    return _SessionFactory

# Alias for compatibility
AsyncSessionFactory = get_session_factory

async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """
    Dependency that provides an AsyncSession for API endpoints.
    """
    factory = get_session_factory()
    async with factory() as session:
        try:
            yield session
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close() 