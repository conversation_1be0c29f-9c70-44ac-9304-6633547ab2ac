import os
import logging
from pydantic_settings import BaseSettings, SettingsConfigDict
from dotenv import load_dotenv

# Get a logger for this module
log = logging.getLogger(__name__)

# Load .env file from the backend directory or project root if needed
# Adjust the path as necessary based on where you run the app from
env_path = os.path.join(os.path.dirname(__file__), '..', '..', '.env')
# print(f"Attempting to load .env from: {os.path.abspath(env_path)}") # DEBUG: Removed
load_dotenv(env_path) # Loads .env from backend/
# print(f"DATABASE_URL from os.environ after load_dotenv: {os.environ.get('DATABASE_URL')}") # DEBUG: Removed

class Settings(BaseSettings):
    PROJECT_NAME: str = "LLM Evaluation Platform Backend"
    API_V1_STR: str = "/api/v1"

    # Database
    DATABASE_URL: str = "sqlite+aiosqlite:///./sql_app.db" # Default, should be overridden by .env

    # LLM Provider
    OPENROUTER_API_KEY: str = "YOUR_OPENROUTER_API_KEY_HERE" # Default, should be overridden by .env

    # Define other settings here, e.g., CORS origins

    # Use model_config to load from .env file
    # Note: pydantic-settings automatically reads .env files if python-dotenv is installed
    # and the BaseSettings fields match environment variable names (case-insensitive).
    # However, explicitly loading with load_dotenv provides more control over the .env file location.
    model_config = SettingsConfigDict(env_file='.env', env_file_encoding='utf-8', extra='ignore')


settings = Settings()

# Add logging to check if the API key is loaded
log.info(f"Loaded OPENROUTER_API_KEY: {'*' * (len(settings.OPENROUTER_API_KEY) - 4) + settings.OPENROUTER_API_KEY[-4:] if settings.OPENROUTER_API_KEY and len(settings.OPENROUTER_API_KEY) > 4 else 'Not loaded or too short'}") 

log.info(f"APPLICATION IS USING DATABASE_URL: {settings.DATABASE_URL}")