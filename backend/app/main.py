import logging
from fastapi import <PERSON><PERSON><PERSON>
from fastapi.middleware.cors import CORSMiddleware

from app.api.api import api_router
from app.core.config import settings
from app.db.base import Base # Import Base for Alembic metadata

# --- Logging Configuration ---
log = logging.getLogger(__name__)

# Create FastAPI app instance
app = FastAPI(
    title=settings.PROJECT_NAME,
    openapi_url=f"{settings.API_V1_STR}/openapi.json" # Customize OpenAPI endpoint
)

# --- Middleware ---
# Set all CORS enabled origins
# In production, restrict this to your frontend domain
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins for development
    # allow_origins=[str(origin) for origin in settings.BACKEND_CORS_ORIGINS], # Production example
    allow_credentials=True,
    allow_methods=["*"], # Allows all methods
    allow_headers=["*"], # Allows all headers
)

# --- Event Handlers ---
@app.on_event("startup")
async def startup_event():
    log.info("Starting up...")
    # Database tables are now managed by Alembic migrations
    # See: alembic/README.md for migration management instructions
    # 
    # WARNING: Do not uncomment the following block in production!
    # Doing so would override Alembic migrations and potentially cause data loss.
    # Only use this for development/testing environments when needed.
    #
    # async with engine.begin() as conn:
    #     # await conn.run_sync(Base.metadata.drop_all) # Optional: drop tables on startup
    #     await conn.run_sync(Base.metadata.create_all)
    # log.info("Database tables created (if they didn't exist).")
    log.info("Startup complete.")

@app.on_event("shutdown")
async def shutdown_event():
    log.info("Shutting down...")
    # Add any cleanup logic here if needed
    log.info("Shutdown complete.")


# --- Routers ---
app.include_router(api_router, prefix=settings.API_V1_STR)

# --- Root Endpoint ---
@app.get("/", tags=["Root"])
async def read_root():
    return {"message": f"Welcome to the {settings.PROJECT_NAME}!"} 