version: 1
disable_existing_loggers: False

formatters:
  default:
    format: "[%(asctime)s] %(levelname)s in %(name)s: %(message)s"

handlers:
  console:
    class: logging.StreamHandler
    formatter: default
    stream: ext://sys.stdout

  file:
    class: logging.FileHandler
    formatter: default
    filename: logs/app.log # Will be created inside llm-eval-platform/backend/logs/
    encoding: utf8
    mode: a

root:
  level: INFO
  handlers: [console, file]

loggers:
  uvicorn:
    level: INFO
    handlers: [console, file]
    propagate: False

  uvicorn.error:
    level: INFO
    handlers: [console, file]
    propagate: False

  uvicorn.access:
    level: INFO
    handlers: [console, file]
    propagate: False 