"""Add evaluation_prompt to evaluations table

Revision ID: 424238f025fe
Revises: 57ae700a0c12
Create Date: 2025-05-27 13:06:00.288897

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '424238f025fe'
down_revision: Union[str, None] = '57ae700a0c12'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('evaluations', sa.Column('evaluation_prompt', sa.Text(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('evaluations', 'evaluation_prompt')
    # ### end Alembic commands ###
