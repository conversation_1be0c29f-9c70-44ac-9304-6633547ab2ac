"""Initial schema for PostgreSQL

Revision ID: 57ae700a0c12
Revises: 
Create Date: 2025-05-15 11:26:13.954789

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '57ae700a0c12'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('tasks',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('prompt', sa.Text(), nullable=False),
    sa.Column('status', sa.Enum('PENDING', 'GENERATING', 'COMPLETED', 'EVALUATING', 'EVALUATION_DONE', 'FAILED', name='taskstatus'), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_tasks_id'), 'tasks', ['id'], unique=False)
    op.create_table('evaluations',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('task_id', sa.Integer(), nullable=False),
    sa.Column('status', sa.Enum('PENDING', 'GENERATING', 'COMPLETED', 'EVALUATING', 'EVALUATION_DONE', 'FAILED', name='taskstatus'), nullable=False),
    sa.Column('evaluation_used_blind_ids', sa.Boolean(), server_default='false', nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['task_id'], ['tasks.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_evaluations_id'), 'evaluations', ['id'], unique=False)
    op.create_table('generations',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('task_id', sa.Integer(), nullable=False),
    sa.Column('model_id_used', sa.String(), nullable=False),
    sa.Column('blind_id', sa.String(), nullable=False),
    sa.Column('output_text', sa.Text(), nullable=True),
    sa.Column('reasoning_text', sa.Text(), nullable=True),
    sa.Column('error_message', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['task_id'], ['tasks.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_generations_blind_id'), 'generations', ['blind_id'], unique=True)
    op.create_index(op.f('ix_generations_id'), 'generations', ['id'], unique=False)
    op.create_index(op.f('ix_generations_model_id_used'), 'generations', ['model_id_used'], unique=False)
    op.create_table('rankings',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('evaluation_id', sa.Integer(), nullable=False),
    sa.Column('evaluator_model_id', sa.String(), nullable=False),
    sa.Column('ranked_list_json', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('reasoning_text', sa.Text(), nullable=True),
    sa.Column('error_message', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['evaluation_id'], ['evaluations.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_rankings_evaluator_model_id'), 'rankings', ['evaluator_model_id'], unique=False)
    op.create_index(op.f('ix_rankings_id'), 'rankings', ['id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_rankings_id'), table_name='rankings')
    op.drop_index(op.f('ix_rankings_evaluator_model_id'), table_name='rankings')
    op.drop_table('rankings')
    op.drop_index(op.f('ix_generations_model_id_used'), table_name='generations')
    op.drop_index(op.f('ix_generations_id'), table_name='generations')
    op.drop_index(op.f('ix_generations_blind_id'), table_name='generations')
    op.drop_table('generations')
    op.drop_index(op.f('ix_evaluations_id'), table_name='evaluations')
    op.drop_table('evaluations')
    op.drop_index(op.f('ix_tasks_id'), table_name='tasks')
    op.drop_table('tasks')
    # ### end Alembic commands ###
