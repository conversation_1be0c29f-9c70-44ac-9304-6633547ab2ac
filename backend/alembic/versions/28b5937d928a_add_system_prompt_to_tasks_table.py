"""Add system_prompt to tasks table

Revision ID: 28b5937d928a
Revises: 424238f025fe
Create Date: 2025-05-27 14:09:59.960422

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '28b5937d928a'
down_revision: Union[str, None] = '424238f025fe'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('tasks', sa.Column('system_prompt', sa.Text(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('tasks', 'system_prompt')
    # ### end Alembic commands ###
