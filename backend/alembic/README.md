# Database Migrations with Alembic

This project uses Alembic to manage database schema migrations. This README explains how to work with migrations.

## Directory Structure

- `alembic/versions/` - Contains migration scripts
- `alembic/env.py` - Alembic environment configuration

## Current Status

The project is configured to use Alembic for managing the database schema for PostgreSQL. We are starting with a fresh Alembic history, and the initial schema will be generated based on the current SQLAlchemy models defined in `app/db/models.py`.

## How to Create a New Migration

When you need to make changes to the database schema (add a table, add a column, etc.), follow these steps:

1. First, modify the SQLAlchemy models in `app/db/models.py` to reflect the desired changes.

2. Generate a migration script that detects these changes (run from `llm-eval-platform/backend/`):
   ```bash
   poetry run alembic revision --autogenerate -m "Short description of changes"
   ```

3. Review the generated migration script in `alembic/versions/`. Make sure it only includes the changes you intended.

4. Apply the migration to update the database (run from `llm-eval-platform/backend/`):
   ```bash
   poetry run alembic upgrade head
   ```

## Common Alembic Commands

All commands should be run from the `llm-eval-platform/backend/` directory.

* **Generate a new migration (detecting changes):**
  ```bash
  poetry run alembic revision --autogenerate -m "Description"
  ```

* **Create an empty migration (for manual editing):**
  ```bash
  poetry run alembic revision -m "Description"
  ```

* **Apply all pending migrations:**
  ```bash
  poetry run alembic upgrade head
  ```

* **Revert the most recent migration:**
  ```bash
  poetry run alembic downgrade -1
  ```

* **Get current migration status:**
  ```bash
  poetry run alembic current
  ```

* **View migration history:**
  ```bash
  poetry run alembic history
  ```

## Troubleshooting

If you encounter issues with migrations:

1. **"Can't locate revision identified by..."** - This typically means your database is at a different migration version than expected. Run `poetry run alembic current` to see the current state.

2. **"No such table" errors** - Check if the target table exists in the database before running migrations. Also ensure your `DATABASE_URL` in `.env` and `alembic.ini` is correct.

3. **"Target database is not up to date"** - This might happen if the `alembic_version` table exists but doesn't reflect the latest migration. Use `poetry run alembic stamp head` to mark the current database schema as up-to-date without running migrations (use with caution, typically after manual schema changes or if you are certain the schema matches the models).

## Best Practices

* **Always review generated migrations** before applying them
* **Commit migration scripts to version control**
* **Test migrations on development before production**
* **Never modify existing migration scripts** that have been applied; create new ones instead
* **Keep migrations small and focused** to minimize deployment risk 