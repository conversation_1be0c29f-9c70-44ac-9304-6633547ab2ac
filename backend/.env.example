# Backend Configuration - LLM Evaluation Platform

# Default Database URL (used if no specific AI tool URL is set or for general purposes)
# DATABASE_URL=postgresql+asyncpg://postgres:postgres123@localhost:5432/llm_eval_default
# SQLITE_DATABASE_URL=sqlite:///./sql_app.db

# AI Tool Specific Database URLs
DATABASE_URL_AUGMENT=postgresql+asyncpg://postgres:postgres123@localhost:5433/llm_eval_augment
DATABASE_URL_ROO=postgresql+asyncpg://postgres:postgres123@localhost:5434/llm_eval_roo
DATABASE_URL_CLINE=postgresql+asyncpg://postgres:postgres123@localhost:5435/llm_eval_cline
DATABASE_URL_CURSOR=postgresql+asyncpg://postgres:postgres123@localhost:5436/llm_eval_cursor
DATABASE_URL_COPILOT=postgresql+asyncpg://postgres:postgres123@localhost:5437/llm_eval_copilot
DATABASE_URL_WINDSURF=postgresql+asyncpg://postgres:postgres123@localhost:5438/llm_eval_windsurf

# SQLite Database URL (used if no specific AI tool URL is set or for general purposes)
SQLITE_DATABASE_URL=sqlite:///./sql_app.db

# LLM Provider (OpenRouter)
OPENROUTER_API_KEY="YOUR_OPENROUTER_API_KEY_HERE"
OPENROUTER_API_BASE=https://openrouter.ai/api/v1

# Example Frontend Configuration (usually handled by Vite's import.meta.env)
# VITE_API_BASE_URL=http://localhost:8000/api 