# LLM Evaluation Platform

A full-stack application to evaluate Large Language Model outputs using an N-fold cross-model ranking methodology.

**Stack:**
* **Backend:** FastAPI (Python)
* **Database:** PostgreSQL (with SQLAlchemy async and asyncpg driver)
* **Frontend:** React (TypeScript)
* **Styling:** Tailwind CSS
* **LLM Interaction:** OpenRouter API
* **Deployment:** <PERSON>bernet<PERSON> (k3s), Docker, GitHub Actions CI/CD

## Project Structure

```
llm-eval-platform/
├── backend/                   # FastAPI backend
│   ├── alembic/             # Alembic migrations for PostgreSQL
│   │   ├── versions/
│   │   └── env.py
│   ├── app/
│   │   ├── api/               # API endpoints
│   │   │   ├── endpoints/
│   │   │   └── api.py         # API router aggregation
│   │   ├── core/              # Core configs
│   │   ├── crud/              # Database operations
│   │   ├── db/                # Database models and connection
│   │   ├── schemas/           # Pydantic schemas
│   │   ├── services/          # Business logic
│   │   └── main.py            # FastAPI application entrypoint
│   ├── scripts/             # Utility scripts (e.g., data migration)
│   ├── log_config.yaml      # Centralized logging configuration
│   ├── alembic.ini          # Alembic configuration file
│   ├── pyproject.toml         # Python dependencies (Poetry)
│   ├── Dockerfile           # Docker configuration for backend
│   ├── start.sh             # Script to run the backend with Uvicorn and logging config
│   └── .env                   # Environment variables (create from .env.example)
│
├── frontend/                  # React frontend
│   ├── src/
│   │   ├── api/               # API client
│   │   ├── components/        # React components
│   │   ├── App.tsx            # Main application component
│   │   └── main.tsx           # Entry point
│   ├── package.json           # Node dependencies
│   ├── Dockerfile           # Docker configuration for frontend
│   ├── nginx.conf           # Nginx configuration for production
│   └── vite.config.ts         # Vite configuration
│
├── k8s/                       # Kubernetes manifests
│   ├── namespace.yaml         # Kubernetes namespace
│   ├── postgresql.yaml        # PostgreSQL deployment
│   ├── backend.yaml           # Backend deployment and service
│   └── frontend.yaml          # Frontend deployment and service
│
├── scripts/                   # Deployment scripts
│   ├── deploy-local.sh        # Local k3s deployment script
│   └── cleanup.sh             # Cleanup script for k3s
│
├── .github/workflows/         # CI/CD pipelines
│   └── ci-cd.yaml             # GitHub Actions workflow
│
├── docs/                      # Documentation
│   └── cicd/                  # CI/CD documentation
│       └── README.md          # Comprehensive CI/CD guide
│
├── .env.example               # Example environment variables
└── README.md                  # This file
```

## Quick Start

### Local Development (Traditional)

1. **Backend Setup**:
   ```bash
   cd backend
   cp ../.env.example .env
   # Edit .env with your configuration
   poetry install
   poetry run alembic upgrade head
   ./start.sh
   ```

2. **Frontend Setup**:
   ```bash
   cd frontend
   npm install
   npm run dev
   ```

### Kubernetes Deployment (Recommended)

For a production-like environment using Kubernetes:

1. **Prerequisites**:
   - k3s installed (`curl -sfL https://get.k3s.io | sh -`)
   - Docker installed
   - kubectl configured

2. **Quick Deploy**:
   ```bash
   ./scripts/deploy-local.sh
   ```

3. **Access Application**:
   - Frontend: Check service with `kubectl get services -n llm-eval`
   - Backend API: `kubectl port-forward service/backend-service 8000:8000 -n llm-eval`

4. **Cleanup**:
   ```bash
   ./scripts/cleanup.sh
   ```

## CI/CD Pipeline

The project includes a comprehensive CI/CD pipeline using GitHub Actions:

- **Continuous Integration**: Automated testing and linting for both backend and frontend
- **Container Building**: Automatic Docker image building and pushing to GitHub Container Registry
- **Continuous Deployment**: Automatic deployment to k3s cluster on main branch pushes

### Pipeline Stages:
1. **Backend CI**: Python linting (Flake8) and testing (Pytest)
2. **Frontend CI**: TypeScript linting (ESLint) and building
3. **Build & Push**: Docker image creation and registry push
4. **Deploy**: Automatic deployment to Kubernetes cluster

For detailed CI/CD setup and configuration, see [docs/cicd/README.md](docs/cicd/README.md).

## Setup

### Prerequisites
- Python 3.12+ (as per recent project dependencies)
- Node.js 20+
- [Poetry](https://python-poetry.org/docs/#installation) (for Python dependency management)
- PostgreSQL (e.g., PostgreSQL 13+ installed and running) OR use Kubernetes deployment
- OpenRouter API key (sign up at [OpenRouter](https://openrouter.ai/))
- Docker (for containerized deployment)
- k3s (for Kubernetes deployment)

### Backend Setup

1.  Navigate to the backend directory:
    ```bash
    cd backend
    ```

2.  Copy the environment variables file and edit it:
    ```bash
    cp ../.env.example .env
    ```
   
3.  Configure your `.env` file. You'll need to set:
    *   `OPENROUTER_API_KEY="your-api-key-here"`
    *   `DATABASE_URL="postgresql+asyncpg://user:password@host:port/database_name"` (replace with your PostgreSQL connection details)
    *   `SQLITE_DATABASE_URL="sqlite:///./sql_app.db"` (if you plan to use the included SQLite to PostgreSQL migration script)

4.  Install dependencies using Poetry:
    ```bash
    poetry install
    ```

5.  Ensure your PostgreSQL database is created and accessible with the credentials specified in `.env`.

6.  Apply database migrations (from the `backend` directory with Poetry environment activated):
    ```bash
    poetry shell
    alembic upgrade head
    ```
    (If you are not already in the poetry shell, use `poetry run alembic upgrade head`)

### Frontend Setup

1.  Navigate to the frontend directory:
    ```bash
    cd frontend
    ```

2.  Install dependencies:
    ```bash
    npm install
    ```

## Running the Application

### Running the Backend

1.  From the `backend` directory (with the Poetry environment activated, or use `poetry run`):
    ```bash
    ./start.sh
    ```
    This script uses Uvicorn with the custom logging configuration.
   
    The API will be available at [http://localhost:8000](http://localhost:8000)
   
    API documentation is available at [http://localhost:8000/docs](http://localhost:8000/docs)

### Running the Frontend

1.  From the `frontend` directory:
    ```bash
    npm run dev
    ```
   
    The application will be available at [http://localhost:3000](http://localhost:3000)

## Usage

1.  Enter a prompt for the LLMs to respond to.
2.  Select models for generation.
3.  Wait for the outputs to be generated.
4.  Optionally, choose to use Blind IDs for an evaluation.
5.  Select evaluator models to rank the outputs.
6.  View the evaluation report with rankings and reasoning.
7.  Aggregate results from multiple evaluators.

## Deployment Options

### 1. Traditional Development
- Run backend and frontend separately on your local machine
- Use local PostgreSQL instance
- Good for development and testing

### 2. Docker Compose (Future Enhancement)
- Containerized services with Docker Compose
- Isolated environment with all dependencies
- Good for consistent development environments

### 3. Kubernetes (Current)
- Full Kubernetes deployment with k3s
- Production-ready with health checks, scaling, and monitoring
- Automated CI/CD pipeline
- Good for production and staging environments

## Development Notes

- The backend uses FastAPI's background tasks for LLM generation and evaluation.
- The application now uses PostgreSQL as its primary database.
- All services are containerized with Docker for consistent deployment
- Kubernetes manifests provide production-ready deployment configuration
- CI/CD pipeline ensures code quality and automated deployment
- For a production deployment, consider:
  - Replacing background tasks with a proper job queue (Celery, RQ, etc.).
  - Adding authentication and rate limiting.
  - Setting up proper CORS configuration if deploying frontend and backend separately.
  - Implementing monitoring and logging solutions.
- The `backend/scripts/migrate_sqlite_to_postgres.py` script is available if you need to migrate data from a previous SQLite setup. 

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Ensure tests pass (`poetry run pytest` for backend, `npm run lint` for frontend)
5. Submit a pull request

The CI/CD pipeline will automatically test your changes and deploy them if merged to main. 