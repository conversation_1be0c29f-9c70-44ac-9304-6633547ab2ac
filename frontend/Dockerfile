# Multi-stage build for React frontend with optimized caching
FROM node:20-alpine AS builder

# Install dependencies for building (if needed)
RUN apk add --no-cache git

# Set work directory
WORKDIR /app

# Copy package files first (for better dependency caching)
COPY package*.json ./

# Install all dependencies (including devDependencies for build)
# This layer will be cached unless package.json or package-lock.json changes
RUN npm ci --frozen-lockfile

# Copy source code (this changes frequently, so put it after dependencies)
COPY . .

# Build the application
RUN npm run build

# Production stage with nginx
FROM nginx:alpine AS production

# Install wget for health checks
RUN apk add --no-cache wget

# Copy custom nginx configuration
COPY nginx.conf /etc/nginx/nginx.conf

# Copy built assets from builder stage
COPY --from=builder /app/dist /usr/share/nginx/html

# Create non-root user for nginx (if not exists)
RUN addgroup -g 1001 -S nginx || true && \
    adduser -S -D -H -u 1001 -h /var/cache/nginx -s /sbin/nologin -G nginx -g nginx nginx || true

# Set proper permissions for nginx directories
RUN chown -R nginx:nginx /usr/share/nginx/html && \
    chown -R nginx:nginx /var/cache/nginx && \
    chown -R nginx:nginx /var/log/nginx && \
    chown -R nginx:nginx /etc/nginx/conf.d && \
    chmod -R 755 /var/log/nginx && \
    mkdir -p /tmp && \
    chown -R nginx:nginx /tmp

# Switch to non-root user
USER nginx

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:3000/health || exit 1

# Start nginx
CMD ["nginx", "-g", "daemon off;"] 