// Add type declarations related to react-markdown
declare module 'react-syntax-highlighter/dist/esm/styles/prism';
declare module 'rehype-raw';

// Extend ReactMarkdown's Props type
declare module 'react-markdown' {
  import { FC } from 'react';
  
  export interface Components {
    [key: string]: React.ComponentType<object>;
  }
  
  export interface ReactMarkdownProps {
    children: string;
    className?: string;
    components?: Components;
    rehypePlugins?: unknown[];
  }
  
  const ReactMarkdown: FC<ReactMarkdownProps>;
  export default ReactMarkdown;
} 