import { useEffect, useRef, useState } from 'react';
import useTaskStore from '../store/taskStore';
import HistorySidebar from '../components/HistorySidebar';
import NewConversationPage from './NewConversationPage';
import ViewTaskPage from './ViewTaskPage';
import Logo from '../assets/logo.svg';
import ThemeToggle from '../components/ThemeToggle';
import SystemStatus from '../components/SystemStatus';
import { PanelLeftOpen, PanelLeftClose, SquarePen, PanelLeft } from 'lucide-react';

function EvaluationPlatformPage() {
  const { 
    selectedTaskId, 
    setSelectedTaskId, 
    taskHistory, 
    isLoadingHistory,
    loadTaskHistory,
    errorLoadingHistory,
    loadAvailableModels,
    deleteTask,
    pageError,
    tasks,
    cleanupTask
  } = useTaskStore();

  const [isDesktopSidebarCollapsed, setIsDesktopSidebarCollapsed] = useState(false);
  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);

  // Use ref to track if the component is unmounting
  const isUnmountingRef = useRef(false);

  // Create refs for tasks and cleanupTask to use in the unmount cleanup
  const tasksRef = useRef(tasks);
  const cleanupTaskRef = useRef(cleanupTask);

  // Update refs on every render so cleanup has latest values
  useEffect(() => {
    tasksRef.current = tasks;
    cleanupTaskRef.current = cleanupTask;
  });

  // Load models and task history on component mount
  useEffect(() => {
    loadAvailableModels();
    loadTaskHistory();
  }, [loadAvailableModels, loadTaskHistory]);
  
  // Only clean up SSE connections when the component is truly unmounting
  useEffect(() => {
    // Return cleanup function
    return () => {
      isUnmountingRef.current = true; // This indicates the page itself is unmounting
      console.log('[EvalPlatformPage] Component is unmounting, cleaning up all active SSE connections');
      
      // Clean up only active tasks, using refs for latest values
      Object.keys(tasksRef.current).forEach((taskIdStr) => {
        const taskId = parseInt(taskIdStr, 10);
        const task = tasksRef.current[taskId];
        // Check if the task object exists and if it has an active SSE connection or is streaming
        if (task && (task.sseEventSource || task.isStreaming)) {
          console.log(`[EvalPlatformPage] Final cleanup: Cleaning up task ${taskId}`);
          cleanupTaskRef.current(taskId);
        }
      });
    };
  }, []); // Empty dependency array ensures this runs only on mount and unmount

  // Handle selecting a task from history
  const handleSelectHistoryTask = (taskId: number) => {
    setSelectedTaskId(taskId);
    setIsMobileSidebarOpen(false); // Close mobile sidebar when selecting task
  };

  const handleNewConversation = () => {
    setSelectedTaskId(null);
    setIsMobileSidebarOpen(false); // Close mobile sidebar when starting new conversation
  };

  // Handle deleting a task
  const handleDeleteTask = async (taskId: number) => {
    await deleteTask(taskId);
  };

  // Close mobile sidebar when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (isMobileSidebarOpen && 
          (event.target as Element)?.closest('.mobile-sidebar-content') === null && 
          (event.target as Element)?.closest('.mobile-menu-button') === null) {
        setIsMobileSidebarOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [isMobileSidebarOpen]);

  return (
    <div className="flex h-screen">
      {/* Desktop Sidebar - Hidden with w-0 when collapsed */}
      <div 
        className={`hidden md:flex flex-col bg-light-component dark:bg-dark-component border-r border-light-border dark:border-dark-border transition-all duration-300 ease-in-out
                    ${isDesktopSidebarCollapsed ? 'w-0' : 'w-64'} overflow-hidden`}
      >
        <HistorySidebar
          taskHistory={taskHistory}
          isLoading={isLoadingHistory}
          error={errorLoadingHistory}
          onSelectTask={handleSelectHistoryTask}
          onDeleteTask={handleDeleteTask}
          currentTaskId={selectedTaskId}
          onNewConversation={handleNewConversation}
          onToggleDesktopCollapse={() => setIsDesktopSidebarCollapsed(true)} // Pass toggle to HistorySidebar
        />
      </div>

      {/* Mobile Sidebar Overlay */}
      {isMobileSidebarOpen && (
        <>
          {/* Backdrop */}
          <div className="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden" onClick={() => setIsMobileSidebarOpen(false)} />
          
          {/* Mobile Sidebar */}
          <div className="mobile-sidebar-content fixed inset-y-0 left-0 w-64 bg-light-component dark:bg-dark-component border-r border-light-border dark:border-dark-border z-50 md:hidden">
            <HistorySidebar
              taskHistory={taskHistory}
              isLoading={isLoadingHistory}
              error={errorLoadingHistory}
              onSelectTask={handleSelectHistoryTask}
              onDeleteTask={handleDeleteTask}
              currentTaskId={selectedTaskId}
              onNewConversation={handleNewConversation}
              // No onToggleDesktopCollapse for mobile sidebar
            />
          </div>
        </>
      )}

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col min-w-0">
        {/* Header */}
        <header className="bg-gradient-to-r from-light-component to-light-component-subtle dark:from-dark-component dark:to-dark-component-subtle border-b border-light-border/50 dark:border-dark-border/50 sticky top-0 z-20 shadow-sm backdrop-blur-sm transition-all duration-300">
          <div className="h-14 px-4 sm:px-6 lg:px-8 flex justify-between items-center">
            <div className="flex items-center gap-3">
              {/* Mobile Menu Button */}
              <button
                onClick={() => setIsMobileSidebarOpen(!isMobileSidebarOpen)}
                className="mobile-menu-button md:hidden p-2 rounded-lg text-light-secondary dark:text-dark-secondary hover:text-light-primary dark:hover:text-dark-primary hover:bg-light-component-subtle dark:hover:bg-dark-component-subtle transition-colors"
                aria-label={isMobileSidebarOpen ? "Close sidebar" : "Open sidebar"}
              >
                {isMobileSidebarOpen ? <PanelLeftClose size={16} /> : <PanelLeft size={16} />}
              </button>

              {/* Desktop - Open Sidebar Button (shown when collapsed) */}
              {isDesktopSidebarCollapsed && (
                <button
                  onClick={() => setIsDesktopSidebarCollapsed(false)}
                  className="hidden md:block p-2 rounded-lg text-light-secondary dark:text-dark-secondary hover:text-light-primary dark:hover:text-dark-primary hover:bg-light-component-subtle dark:hover:bg-dark-component-subtle transition-colors"
                  title="Open sidebar"
                  aria-label="Open sidebar"
                >
                  <PanelLeftOpen size={16} />
                </button>
              )}
              
              {/* Desktop - New Conversation Icon Button (shown when sidebar collapsed) */}
              {isDesktopSidebarCollapsed && (
                 <button
                  onClick={handleNewConversation}
                  title="New Conversation"
                  className="hidden md:block p-2 rounded-lg text-light-secondary dark:text-dark-secondary hover:text-light-primary dark:hover:text-dark-primary hover:bg-light-component-subtle dark:hover:bg-dark-component-subtle transition-colors"
                >
                  <SquarePen size={16} />
                </button>
              )}
              
              {/* Logo and Title - only show if desktop sidebar is not collapsed OR on mobile */}
              {(!isDesktopSidebarCollapsed || isMobileSidebarOpen) && (
                 <div className="flex items-center gap-3">
                    <div className="relative group">
                      <div className="absolute -inset-0.5 bg-gradient-to-r from-light-accent to-blue-500 dark:from-dark-accent dark:to-blue-400 rounded-lg blur opacity-20 group-hover:opacity-60 transition duration-300"></div>
                      <div className="relative p-2 rounded-lg">
                        <img src={Logo} alt="LLM Eval Platform Logo" className="h-10 w-10" />
                      </div>
                    </div>
                    <div>
                      <h1 className="text-sm sm:text-base font-bold bg-gradient-to-r from-light-primary to-light-secondary dark:from-dark-primary dark:to-dark-secondary bg-clip-text text-transparent">
                        LLM Evaluation Platform
                      </h1>
                      <p className="text-xs text-light-secondary dark:text-dark-secondary hidden sm:block">
                        Advanced AI Model Assessment
                      </p>
                    </div>
                  </div>
              )}
            </div>
            <div className="flex items-center gap-3">
              <SystemStatus />
              <ThemeToggle />
            </div>
          </div>
        </header>
        
        {/* Main Content */}
        <main className="flex-1 overflow-auto">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-3 sm:py-4">
            {pageError && (
              <div 
                className="mb-3 bg-light-error-bg dark:bg-dark-error-bg border-l-4 border-light-error dark:border-dark-error text-light-error dark:text-dark-error p-3 rounded-md shadow-md transition-colors duration-300"
                role="alert"
              >
                <p className="font-bold text-xs">Error</p>
                <p className="text-xs">{pageError}</p>
              </div>
            )}

            {!selectedTaskId ? (
              <NewConversationPage />
            ) : (
              <ViewTaskPage taskId={selectedTaskId} />
            )}
          </div>
        </main>

        {/* Footer */}
        <footer className="border-t border-light-border/30 dark:border-dark-border/30 px-4 sm:px-6 lg:px-8 py-4 text-center text-light-secondary dark:text-dark-secondary text-sm transition-colors duration-300">
          <p>LLM Evaluation Platform &copy; {new Date().getFullYear()}</p>
        </footer>
      </div>
    </div>
  );
}

export default EvaluationPlatformPage; 