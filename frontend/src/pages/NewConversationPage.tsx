import { useState } from 'react';
import PromptInput from '../components/PromptInput';
import useTaskStore from '../store/taskStore';
import { Edit } from 'lucide-react';

function NewConversationPage() {
  const { availableModels, createNewTask } = useTaskStore();
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const handlePromptSubmit = async (prompt: string, modelsToUse: string[], systemPrompt?: string) => {
    if (isSubmitting) return;
    
    setIsSubmitting(true);
    try {
      await createNewTask(prompt, modelsToUse, systemPrompt);
    } finally {
      setIsSubmitting(false);
    }
  };
  
  return (
    <div className="bg-light-component dark:bg-dark-component shadow-lg rounded-lg transition-colors duration-300">
      <div className="flex items-center justify-between px-4 py-3 border-b border-light-border dark:border-dark-border transition-colors duration-300">
        <div className="flex items-center">
          <Edit className="mr-2 text-light-accent dark:text-dark-accent" size={16} />
          <h2 className="text-md font-medium text-light-primary dark:text-dark-primary">New Conversation</h2>
        </div>
      </div>
      <div className="px-4 py-5 sm:p-6">
        <p className="text-sm text-light-secondary dark:text-dark-secondary mb-4 transition-colors duration-300">
          Enter your prompt and select the models you'd like to evaluate.
        </p>
        <PromptInput
          onSubmit={handlePromptSubmit}
          isLoading={isSubmitting}
          availableModels={availableModels}
          isReadOnly={false}
        />
      </div>
    </div>
  );
}

export default NewConversationPage; 