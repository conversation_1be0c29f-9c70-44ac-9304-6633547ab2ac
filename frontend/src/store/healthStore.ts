import { create } from 'zustand';
import { api, HealthResponse, HealthStatus } from '../api/apiClient';

interface HealthStore {
  health: HealthResponse | null;
  isLoading: boolean;
  error: string | null;
  lastChecked: Date | null;
  checkInterval: NodeJS.Timeout | null;

  // Actions
  checkHealth: () => Promise<void>;
  startHealthMonitoring: () => void;
  stopHealthMonitoring: () => void;
}

const HEALTH_CHECK_INTERVAL = 30000; // 30 seconds

const useHealthStore = create<HealthStore>((set, get) => ({
  health: null,
  isLoading: false,
  error: null,
  lastChecked: null,
  checkInterval: null,

  checkHealth: async () => {
    set({ isLoading: true, error: null });
    
    try {
      const healthData = await api.getHealth();
      set({ 
        health: healthData, 
        isLoading: false, 
        lastChecked: new Date(),
        error: null 
      });
    } catch (error) {
      console.error('Health check failed:', error);
      const errorMessage = error instanceof Error ? error.message : 'Health check failed';
      
      // Create a fallback health response for UI display
      const fallbackHealth: HealthResponse = {
        status: HealthStatus.UNHEALTHY,
        timestamp: new Date().toISOString(),
        version: 'unknown',
        database: {
          connection: { status: HealthStatus.UNKNOWN, message: 'Cannot reach server' },
          tables: { status: HealthStatus.UNKNOWN, message: 'Cannot check database' }
        },
        services: {
          api: { status: HealthStatus.UNHEALTHY, message: errorMessage }
        }
      };

      set({ 
        health: fallbackHealth,
        isLoading: false, 
        error: errorMessage,
        lastChecked: new Date()
      });
    }
  },

  startHealthMonitoring: () => {
    const currentInterval = get().checkInterval;
    if (currentInterval) {
      clearInterval(currentInterval);
    }

    // Initial health check
    get().checkHealth();

    // Set up periodic health checks
    const intervalId = setInterval(() => {
      get().checkHealth();
    }, HEALTH_CHECK_INTERVAL);

    set({ checkInterval: intervalId });
  },

  stopHealthMonitoring: () => {
    const currentInterval = get().checkInterval;
    if (currentInterval) {
      clearInterval(currentInterval);
      set({ checkInterval: null });
    }
  }
}));

export default useHealthStore; 