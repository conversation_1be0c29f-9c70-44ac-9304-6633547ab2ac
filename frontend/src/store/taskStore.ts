import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import axios from 'axios';
import { api, Generation, EvaluationReportResponse, sse, StreamEventData, Evaluation, TaskHistoryItem, TaskStatusResponse, EvaluationAggregationResponse, AggregationAlgorithmEnum } from '../api/apiClient';

// Interface for the state of a single task/conversation
export interface TaskState {
  id: number;
  prompt: string;
  system_prompt?: string;  // Add system_prompt field
  requestedModels: string[]; // Models selected for generation
  outputs: Generation[]; // Generation type from apiClient.ts already includes blind_id
  currentEvaluationId: number | null;
  currentReport: EvaluationReportResponse | null;
  evaluations?: Evaluation[]; // ADDED: To store full evaluation objects linked to the task
  cacheTimestamp?: number; // ADDED: Timestamp for cache validity
  
  // Status flags
  isGenerating: boolean; // Backend status - generation is in progress on server
  isStreaming: boolean;  // Frontend status - we're actively receiving SSE updates
  isEvaluating: boolean; // Is this task being evaluated
  
  errorMessage: string | null; // Error specific to this task
  sseEventSource: EventSource | null;
  modelsDoneStreamingThisTask: Set<string>;
  activeEvaluationPollIntervalId?: NodeJS.Timeout;
  activeGenerationPollIntervalId?: NodeJS.Timeout; // Changed for generation polling
  taskStatusFromBackend?: string; // Overall status from backend

  // New state for aggregation - per algorithm
  aggregatedReportsByAlgorithm: Partial<Record<AggregationAlgorithmEnum, EvaluationAggregationResponse>> | null;
  isAggregating: boolean; // Remains a general flag, could be made per-algo if needed
  aggregationError: string | null; // Remains general, or could be per-algo
  
  // ADDED: Chunk buffering for performance
  chunkBuffer: Record<string, { output_text: string; reasoning_text: string }>;
  bufferFlushTimer: number | null;
  lastFlushTime: number;
}

interface TaskStore {
  // Core state
  selectedTaskId: number | null;
  tasks: Record<number, TaskState>;
  taskHistory: TaskHistoryItem[];
  availableModels: string[];
  activeHistoryPollIntervalId: NodeJS.Timeout | null; // For history polling
  
  // Status flags
  isLoadingModels: boolean;
  isLoadingHistory: boolean;
  pageError: string | null;
  errorLoadingHistory: string | null;
  
  // Actions
  setSelectedTaskId: (taskId: number | null) => void;
  loadAvailableModels: () => Promise<void>;
  loadTaskHistory: () => Promise<void>;
  createNewTask: (prompt: string, modelsToUse: string[], systemPrompt?: string) => Promise<number | null>;
  loadTaskDetails: (taskId: number) => Promise<void>;
  deleteTask: (taskId: number) => Promise<void>;
  evaluateTask: (taskId: number, selectedEvalModels: string[], evaluationUsedBlindIds: boolean, customEvaluationPrompt?: string) => Promise<void>;
  fetchAggregatedReport: (taskId: number, evaluationId: number, algorithm: AggregationAlgorithmEnum, evaluatorWeights?: Record<string, number>) => Promise<void>;
  
  // SSE management
  initiateSseForTask: (taskId: number) => void;
  closeSseForTask: (taskId: number) => void;
  cleanupTask: (taskId: number) => void;

  // Polling
  startPollingEvaluation: (taskId: number, evaluationId: number) => void;
  startPollingGenerationStatus: (taskId: number) => void;
  stopPollingGenerationStatus: (taskId: number) => void;
  startPollingTaskHistory: () => void;
  stopPollingTaskHistory: () => void;
  
  // ADDED: Chunk buffering methods
  flushChunkBuffer: (taskId: number) => void;
}

const GENERATION_POLL_INTERVAL = 7000; 
const MAX_GENERATION_POLL_ERRORS = 3;
const HISTORY_POLL_INTERVAL = 5000; // e.g., 5 seconds for history list polling
const ACTIVE_TASK_STATUSES_FOR_HISTORY_POLL = ['PENDING', 'GENERATING', 'EVALUATING'];

// Helper function to compare task history arrays
const  areHistoriesEqual = (
  historyA: TaskHistoryItem[], 
  historyB: TaskHistoryItem[]
): boolean => {
  if (historyA.length !== historyB.length) {
    return false;
  }
  if (historyA.length === 0) { // Both are empty
      return true;
  }
  for (let i = 0; i < historyA.length; i++) {
    if (
      historyA[i].id !== historyB[i]?.id ||
      historyA[i].status !== historyB[i]?.status ||
      historyA[i].prompt_snippet !== historyB[i]?.prompt_snippet
    ) {
      return false; 
    }
  }
  return true; 
};

const useTaskStore = create<TaskStore>()(
  persist(
    (set, get) => ({
  // Core state
  selectedTaskId: null,
  tasks: {},
  taskHistory: [],
  availableModels: [],
  activeHistoryPollIntervalId: null,
  
  // Status flags
  isLoadingModels: false,
  isLoadingHistory: false,
  pageError: null,
  errorLoadingHistory: null,
  
  // Actions
  setSelectedTaskId: (taskId: number | null) => {
    console.log(`[Store] Selecting task ID: ${taskId}`);
    set({ selectedTaskId: taskId });
  },
  
  loadAvailableModels: async () => {
    set({ isLoadingModels: true, pageError: null });
    try {
      const models = await api.getModels();
      set({ availableModels: models, isLoadingModels: false });
    } catch (err: unknown) {
      console.error('Failed to fetch available models:', err);
      let message = 'Failed to fetch available models. Please try refreshing.';
      if (err instanceof Error) {
        message = `Failed to fetch available models: ${err.message}. Please try refreshing.`;
      }
      set({ 
        pageError: message,
        isLoadingModels: false 
      });
    }
  },
  
  loadTaskHistory: async () => {
    const currentHistory = get().taskHistory;
    
    if (currentHistory.length === 0) {
      set({ isLoadingHistory: true, errorLoadingHistory: null });
    } else {
      set({ errorLoadingHistory: null }); // Clear previous error, but don't show full load
    }

    try {
      const response = await api.getTaskHistory();
      const newHistory = response.history;

      if (!areHistoriesEqual(newHistory, currentHistory)) {
        set({ taskHistory: newHistory, isLoadingHistory: false });
      } else {
        // History is the same, just ensure loading is false.
        set({ isLoadingHistory: false });
      }
      
      const activeTasks = newHistory.some(task => 
        ACTIVE_TASK_STATUSES_FOR_HISTORY_POLL.includes(task.status)
      );
      if (activeTasks) {
        get().startPollingTaskHistory();
      } else {
        get().stopPollingTaskHistory();
      }
      
    } catch (err: unknown) {
      console.error("Failed to fetch task history:", err);
      let message = "Failed to load history";
      if (err instanceof Error) {
        message = `Failed to load history: ${err.message}`;
      }
      set({ 
        errorLoadingHistory: message, 
        isLoadingHistory: false 
      });
    }
  },
  
  createNewTask: async (prompt: string, modelsToUse: string[], systemPrompt?: string) => {
    set({ pageError: null });
    try {
      const response = await api.createTask(prompt, modelsToUse, systemPrompt);
      const newTaskId = response.task_id;
      console.log(`[CreateTask] Task ${newTaskId} created successfully with models: ${response.requested_models.join(', ')} and status ${response.status}`);
      
      set(state => ({
        tasks: {
          ...state.tasks,
          [newTaskId]: {
            id: newTaskId,
            prompt,
            system_prompt: systemPrompt,
            requestedModels: response.requested_models || modelsToUse,
            outputs: [],
            currentEvaluationId: null,
            currentReport: null,
            evaluations: [], // Initialize as empty array
            cacheTimestamp: Date.now(), // Initialize cache timestamp
            isGenerating: response.status === 'GENERATING',
            isStreaming: false,
            isEvaluating: false,
            errorMessage: null,
            sseEventSource: null,
            modelsDoneStreamingThisTask: new Set<string>(),
            taskStatusFromBackend: response.status,
            aggregatedReportsByAlgorithm: null,
            isAggregating: false,
            aggregationError: null,
            chunkBuffer: {},
            bufferFlushTimer: null,
            lastFlushTime: 0,
          }
        },
        selectedTaskId: newTaskId
      }));
      
      await get().loadTaskHistory(); // This will also trigger history polling if the new task is PENDING
      
      console.log(`[CreateTask] Calling initiateSseForTask for task ${newTaskId}`);
      get().initiateSseForTask(newTaskId);

      return newTaskId;
    } catch (err: unknown) {
      console.error('Failed to create task:', err);
      let detailMessage = 'Failed to create task.';
      if (axios.isAxiosError(err) && err.response?.data?.detail) {
        detailMessage = `Failed to create task: ${err.response.data.detail}`;
      } else if (err instanceof Error) {
        detailMessage = `Failed to create task: ${err.message}`;
      }
      set({ 
        pageError: detailMessage 
      });
      return null;
    }
  },
  
  loadTaskDetails: async (taskId: number) => {
    try {
      const taskDetails = await api.getTaskDetails(taskId);
      console.log(`[LoadDetails] Task ${taskId} details loaded, status: ${taskDetails.status}`);
      
      set(state => {
        const existingTask = state.tasks[taskId];
        
        // When loading details, populate the evaluations array on the taskState
        const updatedEvaluations = taskDetails.evaluations || []; 
        
        return {
          tasks: {
            ...state.tasks,
            [taskId]: {
              ...existingTask,
              id: taskDetails.id,
              prompt: taskDetails.prompt,
              system_prompt: taskDetails.system_prompt,
              requestedModels: taskDetails.requested_models || [],
              outputs: taskDetails.generations?.map(g => { 
                const existingGen = existingTask?.outputs.find(o => o.id === g.id || o.model_id_used === g.model_id_used);
                return {
                  ...g,
                  reasoning_text: g.reasoning_text ?? existingGen?.reasoning_text ?? null 
                };
              }) || [],
              evaluations: updatedEvaluations, // Store fetched evaluations
              cacheTimestamp: Date.now(), // Update cache timestamp
              taskStatusFromBackend: taskDetails.status,
              isGenerating: taskDetails.status === 'GENERATING',
              isEvaluating: taskDetails.status === 'EVALUATING',
              errorMessage: null, // Clear error on successful load
              // Preserve sseEventSource and modelsDoneStreamingThisTask if they exist
              sseEventSource: existingTask?.sseEventSource || null, 
              modelsDoneStreamingThisTask: existingTask?.modelsDoneStreamingThisTask || new Set<string>(),
              // Update currentEvaluationId and currentReport if an evaluation is active or just finished
              // This part might need more sophisticated logic if multiple evaluations can be "current"
              currentEvaluationId: updatedEvaluations.find(ev => ev.status === 'EVALUATING')?.id || 
                                   updatedEvaluations.sort((a,b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime()).find(ev => ev.status === 'EVALUATION_DONE')?.id || 
                                   existingTask?.currentEvaluationId || null,
            }
          }
        };
      });
      
      const state = get();
      const taskState = state.tasks[taskId];
      if (taskState && taskState.isEvaluating && taskState.currentEvaluationId && !taskState.activeEvaluationPollIntervalId) {
        console.log(`[LoadDetails] Task ${taskId} is EVALUATING, starting eval poll.`);
        get().startPollingEvaluation(taskId, taskState.currentEvaluationId);
      }

      // If loaded details indicate task is still PENDING or GENERATING, and no SSE, try to initiate SSE
      // This handles cases like page refresh on a generating task.
      if ((taskDetails.status === 'PENDING' || taskDetails.status === 'GENERATING') && !taskState?.sseEventSource && !taskState?.isStreaming) {
        console.log(`[LoadDetails] Task ${taskId} is ${taskDetails.status} and no active SSE. Attempting to initiate SSE.`);
        get().initiateSseForTask(taskId);
      }
      
    } catch (err: unknown) {
      console.error(`Failed to load task details for ${taskId}:`, err);
      let detailMessage = 'Error loading task details';
      if (axios.isAxiosError(err) && err.response?.data?.detail) {
        detailMessage = `Failed to load task details: ${err.response.data.detail}`;
      } else if (err instanceof Error) {
        detailMessage = `Failed to load task details: ${err.message}`;
      }
      set(state => ({
        tasks: {
          ...state.tasks,
          [taskId]: {
            ...(state.tasks[taskId] || { // Provide defaults if task is entirely new
              id: taskId,
              prompt: 'Error loading prompt',
              system_prompt: '',
              requestedModels: [],
              outputs: [],
              currentEvaluationId: null,
              currentReport: null,
              cacheTimestamp: 0, // Set to an old timestamp on error
              isGenerating: false,
              isStreaming: false,
              isEvaluating: false,
              sseEventSource: null,
              modelsDoneStreamingThisTask: new Set<string>(),
              taskStatusFromBackend: 'ERROR',
              aggregatedReportsByAlgorithm: null,
              isAggregating: false,
              aggregationError: null,
              chunkBuffer: {},
              bufferFlushTimer: null,
              lastFlushTime: 0,
            }),
            errorMessage: detailMessage
          }
        }
      }));
    }
  },
  
  deleteTask: async (taskId: number) => {
    get().cleanupTask(taskId); 
    
    try {
      await api.deleteTask(taskId);
      
      set(state => {
        const newTasks = { ...state.tasks };
        delete newTasks[taskId];
        
        const newSelectedTaskId = state.selectedTaskId === taskId ? null : state.selectedTaskId;
        
        return {
          tasks: newTasks,
          selectedTaskId: newSelectedTaskId
        };
      });
      
      await get().loadTaskHistory(); // Refresh history and update polling status
      
    } catch (err: unknown) {
      console.error(`Failed to delete task ${taskId} from backend:`, err);
      let message = `Failed to delete task ${taskId}.`;
       if (axios.isAxiosError(err) && err.response?.data?.detail) {
        message = `Failed to delete task ${taskId}: ${err.response.data.detail}`;
      } else if (err instanceof Error) {
        message = `Failed to delete task ${taskId}: ${err.message}`;
      }
      console.error(message); 
    }
  },
  
  evaluateTask: async (taskId: number, selectedEvalModels: string[], evaluationUsedBlindIds: boolean, customEvaluationPrompt?: string) => {
    const currentTask = get().tasks[taskId];
    if (!currentTask) {
      console.error("[EvaluateTask] Task not found in store:", taskId);
      set({ pageError: `Task ${taskId} not found for evaluation.` });
      return;
    }

    set(state => ({
      tasks: {
        ...state.tasks,
        [taskId]: {
          ...state.tasks[taskId],
          isEvaluating: true,
          currentEvaluationId: null, // Reset any previous one
          currentReport: null,       // Clear old report
          errorMessage: null,         // Clear previous errors for this task
        },
      },
      pageError: null, // Clear page-level errors
    }));
    
    try {
      console.log(`[Store] evaluateTask: Initiating evaluation for task ${taskId} with models: ${selectedEvalModels.join(', ')}, evaluationUsedBlindIds: ${evaluationUsedBlindIds}`);
      const response = await api.evaluateTask(taskId, selectedEvalModels, evaluationUsedBlindIds, customEvaluationPrompt);
      const evaluationId = response.evaluation_id;
      console.log(`[Store] evaluateTask: Evaluation ${evaluationId} started for task ${taskId}. Status: ${response.status}`);
      
      set(state => ({
        tasks: {
          ...state.tasks,
          [taskId]: {
            ...state.tasks[taskId],
            currentEvaluationId: evaluationId,
            // isEvaluating might still be true, backend will update actual status
            // taskStatusFromBackend could be updated here if API returns full task, but it returns EvaluateResponse
          }
        }
      }));
      get().startPollingEvaluation(taskId, evaluationId);
      get().loadTaskHistory(); // Refresh history as task status likely changed to EVALUATING
    } catch (err: unknown) {
      console.error(`[Store] Failed to evaluate task ${taskId}:`, err);
      let detailMessage = `Failed to start evaluation for task ${taskId}.`;
      if (axios.isAxiosError(err) && err.response?.data?.detail) {
        detailMessage = `Failed to start evaluation: ${err.response.data.detail}`;
      } else if (err instanceof Error) {
        detailMessage = `Failed to start evaluation: ${err.message}`;
      }
      set(state => ({
        tasks: {
          ...state.tasks,
          [taskId]: {
            ...state.tasks[taskId],
            isEvaluating: false,
            errorMessage: detailMessage,
          }
        },
      }));
    }
  },
      
      fetchAggregatedReport: async (taskId: number, evaluationId: number, algorithm: AggregationAlgorithmEnum, evaluatorWeights?: Record<string, number>) => {
        set(state => ({
          tasks: {
            ...state.tasks,
            [taskId]: {
              ...state.tasks[taskId],
              isAggregating: true,
              aggregationError: null,
          }
        }
      }));

        try {
          const report = await api.getAggregatedEvaluationReport(evaluationId, algorithm, evaluatorWeights);
          set(state => ({
            tasks: {
              ...state.tasks,
              [taskId]: {
                ...state.tasks[taskId],
                aggregatedReportsByAlgorithm: {
                  ...(state.tasks[taskId]?.aggregatedReportsByAlgorithm || {}),
                  [algorithm]: report,
                },
                isAggregating: false,
              }
            }
          }));
        } catch (err: unknown) {
          console.error(`Error fetching aggregated report for task ${taskId}, eval ${evaluationId}:`, err);
          let detailMessage = 'Failed to fetch aggregated report.';
          if (axios.isAxiosError(err) && err.response?.data?.detail) {
            detailMessage = `Failed to fetch aggregated report: ${err.response.data.detail}`;
          } else if (err instanceof Error) {
            detailMessage = `Failed to fetch aggregated report: ${err.message}`;
          }
          set(state => ({
            tasks: {
              ...state.tasks,
              [taskId]: {
                ...state.tasks[taskId],
                isAggregating: false,
                aggregationError: detailMessage,
              }
            }
          }));
    }
  },
  
  startPollingEvaluation: (taskId: number, evaluationId: number) => {
    const state = get();
    const taskState = state.tasks[taskId];
    
    if (taskState?.activeEvaluationPollIntervalId) {
      console.log(`[PollingEval] Task ${taskId} eval poll already active.`);
      return;
    }
    console.log(`[PollingEval] Starting evaluation polling for task ${taskId}, eval ID ${evaluationId}.`);
    
    let consecutiveErrors = 0;
    const maxConsecutiveErrors = 5;
    
    const pollInterval = setInterval(async () => {
      try {
        // Instead of full getTaskDetails, ideally we'd have a lighter getEvaluationStatus endpoint
        // For now, using getTaskDetails as it contains evaluation info
        const taskDetails = await api.getTaskDetails(taskId); 
        consecutiveErrors = 0;
        
        const relevantEvaluation = taskDetails.evaluations?.find(
          (ev: Evaluation) => ev.id === evaluationId
        );
        
        if (relevantEvaluation) {
          const evalStatus = relevantEvaluation.status;
          console.log(`[PollingEval] Task ${taskId}, Eval ${evaluationId} status: ${evalStatus}.`);
          
          if (evalStatus === 'EVALUATION_DONE' || evalStatus === 'FAILED') {
            clearInterval(pollInterval);
            
            set(state => ({
              tasks: {
                ...state.tasks,
                [taskId]: {
                  ...state.tasks[taskId],
                  isEvaluating: false,
                  currentReport: {
                    evaluation_id: relevantEvaluation.id,
                    task_id: taskId,
                    status: relevantEvaluation.status,
                    rankings: relevantEvaluation.rankings?.map(r => ({...r, ranked_list_json: r.ranked_list_json as number[]})) || []
                  },
                  currentEvaluationId: relevantEvaluation.id, // Keep current ID
                  activeEvaluationPollIntervalId: undefined,
                  // taskStatusFromBackend should be updated by loadTaskDetails or history load
                  errorMessage: evalStatus === 'FAILED' 
                    ? (relevantEvaluation.rankings?.find(r => r.error_message)?.error_message || state.tasks[taskId]?.errorMessage || 'Evaluation process failed.') 
                    : state.tasks[taskId]?.errorMessage,
                      aggregatedReportsByAlgorithm: state.tasks[taskId]?.aggregatedReportsByAlgorithm,
                      isAggregating: state.tasks[taskId]?.isAggregating || false,
                      aggregationError: state.tasks[taskId]?.aggregationError || null,
                }
              }
            }));
            console.log(`[PollingEval] Task ${taskId}, Eval ${evaluationId} finished (${evalStatus}). Stopped poll.`);
            await get().loadTaskDetails(taskId); // Reload details to get final task status etc.
            await get().loadTaskHistory(); // Update history list and its polling
          } else {
            // Still polling, update status if needed
            set(state => ({
              tasks: {
                ...state.tasks,
                [taskId]: {
                  ...state.tasks[taskId],
                  isEvaluating: true, // Ensure it stays true
                  taskStatusFromBackend: taskDetails.status, // Update main task status too
                  currentReport: { // Update report status while polling
                     evaluation_id: relevantEvaluation.id,
                     task_id: taskId,
                     status: relevantEvaluation.status,
                     rankings: relevantEvaluation.rankings?.map(r => ({...r, ranked_list_json: r.ranked_list_json as number[]})) || []
                      },
                      aggregatedReportsByAlgorithm: state.tasks[taskId]?.aggregatedReportsByAlgorithm,
                      isAggregating: state.tasks[taskId]?.isAggregating || false,
                      aggregationError: state.tasks[taskId]?.aggregationError || null,
                }
              }
            }));
          }
        } else {
          consecutiveErrors++;
          console.warn(`[PollingEval] Task ${taskId}, Eval ${evaluationId} not found in details. Attempt ${consecutiveErrors}.`);
          if (consecutiveErrors >= maxConsecutiveErrors) {
            clearInterval(pollInterval);
            set(state => ({
              tasks: {
                ...state.tasks,
                [taskId]: {
                  ...state.tasks[taskId],
                  isEvaluating: false,
                  activeEvaluationPollIntervalId: undefined,
                  errorMessage: state.tasks[taskId]?.errorMessage || `Evaluation ${evaluationId} details not found after multiple attempts.`
                }
              }
            }));
            console.error(`[PollingEval] Task ${taskId}, Eval ${evaluationId} not found after max attempts. Stopped poll.`);
            await get().loadTaskHistory();
          }
        }
      } catch (err: unknown) {
        console.error(`[PollingEval] Error polling evaluation status for task ${taskId}, eval ${evaluationId}:`, err);
        consecutiveErrors++;
        if (consecutiveErrors >= maxConsecutiveErrors) {
          clearInterval(pollInterval);
          set(state => ({
            tasks: {
              ...state.tasks,
              [taskId]: {
                ...state.tasks[taskId],
                isEvaluating: false,
                activeEvaluationPollIntervalId: undefined,
                errorMessage: state.tasks[taskId]?.errorMessage || 'Failed to poll evaluation status.'
              }
            }
          }));
          console.error(`[PollingEval] Task ${taskId}, Eval ${evaluationId} max polling errors. Stopped poll.`);
          await get().loadTaskHistory();
        }
      }
    }, 3000); // Poll every 3 seconds
    
    set(state => ({
      tasks: {
        ...state.tasks,
        [taskId]: {
          ...state.tasks[taskId],
          activeEvaluationPollIntervalId: pollInterval
        }
      }
    }));
  },
  
  initiateSseForTask: (taskId: number) => {
    console.log(`[InitSSE] Attempting for task ${taskId}`);
    const state = get();
    const taskState = state.tasks[taskId];
    
    if (!taskState) {
      console.warn(`[InitSSE] Task ${taskId} not found in state. Aborting SSE setup.`);
      // Optionally, try to load task details first if this happens unexpectedly
      // await get().loadTaskDetails(taskId);
      // taskState = get().tasks[taskId];
      // if (!taskState) {
      //   console.error(`[InitSSE] Task ${taskId} still not found after attempting load. Cannot establish SSE.`);
      //   return;
      // }
      return;
    }
    
    if (taskState.sseEventSource) {
      console.log(`[InitSSE] Task ${taskId} already has/is getting an SSE connection (readyState: ${taskState.sseEventSource.readyState}).`);
      return;
    }
    
    // Allow SSE for PENDING (will transition to GENERATING) or GENERATING
    if (taskState.taskStatusFromBackend !== 'PENDING' && taskState.taskStatusFromBackend !== 'GENERATING') {
      console.log(`[InitSSE] Task ${taskId} is not PENDING or GENERATING (current: ${taskState.taskStatusFromBackend}). Skipping SSE.`);
      // If it was 'COMPLETED' or 'FAILED' but somehow isGenerating is true, fix it.
      if (taskState.isGenerating) {
        set(s => ({ tasks: { ...s.tasks, [taskId]: { ...s.tasks[taskId], isGenerating: false, isStreaming: false } } }));
      }
      return;
    }
    
    console.log(`[InitSSE] Creating SSE for task ${taskId} (Status: ${taskState.taskStatusFromBackend}, Models: ${taskState.requestedModels?.join(', ') || 'N/A'})`);
    
    try {
      const es = sse.connect(
        taskId,
        // onMessage
        (streamData: StreamEventData) => {
          console.log(`[SSE Chunk] Task ${taskId}, Model ${streamData.model}: buffering chunk`);
          
          const currentTask = get().tasks[taskId];
          if (!currentTask) return;

          // Add chunk to buffer
          const modelId = streamData.model;
          const chunkText = streamData.chunk || '';
          const reasoningText = streamData.reasoning_detail || '';

          // Update buffer
          if (!currentTask.chunkBuffer[modelId]) {
            currentTask.chunkBuffer[modelId] = { output_text: '', reasoning_text: '' };
          }
          currentTask.chunkBuffer[modelId].output_text += chunkText;
          currentTask.chunkBuffer[modelId].reasoning_text += reasoningText;

          // Schedule buffer flush if not already scheduled
          if (!currentTask.bufferFlushTimer) {
            const scheduleFlush = () => {
              const now = Date.now();
              const timeSinceLastFlush = now - (currentTask.lastFlushTime || 0);
              
              if (timeSinceLastFlush >= 20) { // 20ms interval for ~50fps
                get().flushChunkBuffer(taskId);
              } else {
                // Schedule next check
                const timerId = requestAnimationFrame(scheduleFlush);
                set(state => ({
                  tasks: {
                    ...state.tasks,
                    [taskId]: {
                      ...state.tasks[taskId],
                      bufferFlushTimer: timerId
                    }
                  }
                }));
              }
            };
            
            const timerId = requestAnimationFrame(scheduleFlush);
            
            set(state => ({
              tasks: {
                ...state.tasks,
                [taskId]: {
                  ...state.tasks[taskId],
                  bufferFlushTimer: timerId,
                  isStreaming: true,
                  errorMessage: null
                }
              }
            }));
          }
        },
        // onDone
        (doneData: StreamEventData) => {
          console.log(`[SSE Done] Model ${doneData.model} for task ${taskId} finished.`);
          
          // Flush any remaining chunks for this model
          get().flushChunkBuffer(taskId);
          
          set(state => {
            const currentTask = state.tasks[taskId];
            if (!currentTask) return state;

            const newModelsDone = new Set(currentTask.modelsDoneStreamingThisTask).add(doneData.model);
            const allDone = currentTask.requestedModels.every(model => newModelsDone.has(model));
            
            if (allDone) {
              console.log(`[SSE All Done] Task ${taskId} generations complete.`);
              currentTask.sseEventSource?.close();
              get().stopPollingGenerationStatus(taskId);
              get().loadTaskDetails(taskId).then(() => get().loadTaskHistory());
              
              // Clear any remaining buffer timer
              if (currentTask.bufferFlushTimer) {
                cancelAnimationFrame(currentTask.bufferFlushTimer);
              }
            }

            return { tasks: { ...state.tasks, [taskId]: {
              ...currentTask,
              modelsDoneStreamingThisTask: newModelsDone,
              isStreaming: !allDone,
              isGenerating: !allDone && currentTask.isGenerating, 
              sseEventSource: allDone ? null : currentTask.sseEventSource,
              bufferFlushTimer: allDone ? null : currentTask.bufferFlushTimer,
              chunkBuffer: allDone ? {} : currentTask.chunkBuffer,
            }}};
          });
        },
        // onError (model specific error from backend stream)
        (errorData: StreamEventData) => {
          console.error(`[SSE Model Error] Task ${taskId}, Model ${errorData.model}: ${errorData.content}`);
          set(state => {
            const currentTask = state.tasks[taskId];
            if (!currentTask) return state;

            let updatedOutputs = currentTask.outputs.map(o => 
              o.model_id_used === errorData.model ? { ...o, error_message: errorData.content || 'Unknown stream error' } : o
            );
            if (!updatedOutputs.some(o => o.model_id_used === errorData.model)) {
              updatedOutputs.push({ 
                id: Date.now(), 
                task_id: taskId, 
                model_id_used: errorData.model, 
                blind_id: `temp-blind-err-${errorData.model}-${Date.now()}`, // ADDED placeholder
                output_text: null, 
                reasoning_text: null,
                error_message: errorData.content || 'Stream error', 
                created_at: new Date().toISOString() 
              });
            }

            const newModelsDone = new Set(currentTask.modelsDoneStreamingThisTask).add(errorData.model);
            const allDone = currentTask.requestedModels.every(model => newModelsDone.has(model));

            if (allDone) {
                console.log(`[SSE All Done (with errors)] Task ${taskId} finished processing all models.`);
                currentTask.sseEventSource?.close();
                get().stopPollingGenerationStatus(taskId);
                get().loadTaskDetails(taskId).then(() => get().loadTaskHistory());
            }

            return { tasks: { ...state.tasks, [taskId]: {
              ...currentTask,
              outputs: updatedOutputs,
              modelsDoneStreamingThisTask: newModelsDone,
              isStreaming: !allDone,
              isGenerating: !allDone && currentTask.isGenerating,
              errorMessage: currentTask.errorMessage || `Error from model ${errorData.model}: ${errorData.content}`,
              sseEventSource: allDone ? null : currentTask.sseEventSource,
            }}};
          });
        },
        // onConnectionError (network/browser level Event)
        (connectionErrorEvent: Event) => {
          console.error(`[SSE Connection Error] Task ${taskId}:`, connectionErrorEvent);
          const currentTaskState = get().tasks[taskId];
          set(s => ({ tasks: { ...s.tasks, [taskId]: {
            ...s.tasks[taskId],
            isStreaming: false,
            errorMessage: s.tasks[taskId]?.errorMessage || 'SSE connection error. Stream interrupted.',
            sseEventSource: null 
          }}}));

          // If the task was PENDING or GENERATING, start polling for its status as SSE failed
          if (currentTaskState && (currentTaskState.taskStatusFromBackend === 'PENDING' || currentTaskState.taskStatusFromBackend === 'GENERATING')) {
            console.log(`[SSE Connection Error] SSE failed for active task ${taskId}. Starting generation polling.`);
            get().startPollingGenerationStatus(taskId);
          }
        },
        // onOpen
        () => {
          console.log(`[SSE Connected] Task ${taskId}. Stopping any generation polling.`);
          get().stopPollingGenerationStatus(taskId); // Stop polling if SSE (re)connects
          set(s => {
            const currentTask = s.tasks[taskId];
            if (!currentTask) return s;
            console.log(`[SSE Connected] Updating flags for task ${taskId}: isStreaming: true, isGenerating: true, status: GENERATING`);
            return { tasks: { ...s.tasks, [taskId]: {
              ...currentTask,
              isStreaming: true,
              isGenerating: true, 
              taskStatusFromBackend: 'GENERATING',
              errorMessage: null // Clear previous errors like "connection interrupted"
            }}};
          });
          // Potentially refresh history if status changed from PENDING to GENERATING via SSE
          if (get().tasks[taskId]?.taskStatusFromBackend === 'GENERATING' && taskState.taskStatusFromBackend === 'PENDING') {
             get().loadTaskHistory();
          }
        }
      );
      
      set(s => ({ tasks: { ...s.tasks, [taskId]: { ...s.tasks[taskId], sseEventSource: es }} }));
    } catch (err: unknown) {
      console.error(`[InitSSE] EXCEPTION during SSE setup for task ${taskId}:`, err);
      set(s => ({ tasks: { ...s.tasks, [taskId]: {
        ...s.tasks[taskId],
        errorMessage: `Failed to establish streaming connection: ${err instanceof Error ? err.message : String(err)}`,
        isStreaming: false,
      }}}));
      // If setup itself throws an error, and task was active, start polling
      const currentTaskState = get().tasks[taskId];
      if (currentTaskState && (currentTaskState.taskStatusFromBackend === 'PENDING' || currentTaskState.taskStatusFromBackend === 'GENERATING')) {
        get().startPollingGenerationStatus(taskId);
      }
    }
  },
  
  closeSseForTask: (taskId: number) => {
    const taskState = get().tasks[taskId];
    if (taskState?.sseEventSource) {
      console.log(`[SSE Close] Manually closing SSE for task ${taskId}`);
      try {
        // Check if it's actually an EventSource instance with close method
        if (taskState.sseEventSource instanceof EventSource && typeof taskState.sseEventSource.close === 'function') {
          taskState.sseEventSource.close();
        } else {
          // Try to close via global window reference as fallback
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          const globalEventSource = (window as any)[`sse_connection_${taskId}`];
          if (globalEventSource && typeof globalEventSource.close === 'function') {
            console.log(`[SSE Close] Closing SSE via global reference for task ${taskId}`);
            globalEventSource.close();
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            delete (window as any)[`sse_connection_${taskId}`];
          } else {
            console.log(`[SSE Close] No global SSE reference found for task ${taskId}`);
          }
        }
        
        // Clean up global reference
        try {
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          delete (window as any)[`sse_connection_${taskId}`];
        } catch (e) {
          console.error(`[SSE Close] Error cleaning global SSE reference for task ${taskId}:`, e);
        }
      } catch (error) {
        console.error(`[SSE Close] Error closing SSE for task ${taskId}:`, error);
        // Clean up global reference
        try {
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          delete (window as any)[`sse_connection_${taskId}`];
        } catch (e) {
          console.error(`[SSE Close] Error cleaning global SSE reference for task ${taskId}:`, e);
        }
      }
      set(s => ({ tasks: { ...s.tasks, [taskId]: { ...s.tasks[taskId], isStreaming: false, sseEventSource: null }}}));
    }
    
    // ADDED: Clear buffer timer and flush remaining chunks
    const taskState2 = get().tasks[taskId];
    if (taskState2?.bufferFlushTimer) {
      cancelAnimationFrame(taskState2.bufferFlushTimer);
      get().flushChunkBuffer(taskId); // Flush any remaining chunks
    }
  },
  
  cleanupTask: (taskId: number) => {
    const taskState = get().tasks[taskId];
    if (!taskState) return;
    
    console.log(`[CleanupTask] Cleaning up task ${taskId}`);
    
    // Enhanced SSE connection cleanup logic
    if (taskState.sseEventSource) {
      try {
        console.log(`[CleanupTask] Closing SSE for task ${taskId}`, {
          sseEventSource: taskState.sseEventSource,
          readyState: taskState.sseEventSource?.readyState,
          hasCloseMethod: typeof taskState.sseEventSource?.close === 'function'
        });
        
        // Check if it's actually an EventSource instance with close method
        if (taskState.sseEventSource instanceof EventSource && typeof taskState.sseEventSource.close === 'function') {
          taskState.sseEventSource.close();
        } else {
          // Try to close via global window reference as fallback
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          const globalEventSource = (window as any)[`sse_connection_${taskId}`];
          if (globalEventSource && typeof globalEventSource.close === 'function') {
            console.log(`[CleanupTask] Closing SSE via global reference for task ${taskId}`);
            globalEventSource.close();
            // Clean up global reference
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            delete (window as any)[`sse_connection_${taskId}`];
          } else {
            console.warn(`[CleanupTask] Invalid SSE EventSource for task ${taskId}, skipping close`);
          }
        }
      } catch (error) {
        console.error(`[CleanupTask] Error closing SSE for task ${taskId}:`, error);
        // Clean up global reference even if close fails
        try {
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          delete (window as any)[`sse_connection_${taskId}`];
        } catch (e) {
          console.error(`[CleanupTask] Error cleaning global SSE reference for task ${taskId}:`, e);
        }
      }
    }
    
    if (taskState.activeEvaluationPollIntervalId) {
      console.log(`[CleanupTask] Clearing evaluation poll for task ${taskId}`);
      clearInterval(taskState.activeEvaluationPollIntervalId);
    }
    if (taskState.activeGenerationPollIntervalId) { // Also clear generation poll
      console.log(`[CleanupTask] Clearing generation poll for task ${taskId}`);
      clearInterval(taskState.activeGenerationPollIntervalId);
    }
    
    // ADDED: Clear chunk buffer timer
    if (taskState.bufferFlushTimer) {
      console.log(`[CleanupTask] Clearing chunk buffer timer for task ${taskId}`);
      cancelAnimationFrame(taskState.bufferFlushTimer);
    }
    
    // Reset relevant parts of task state if needed, or rely on deletion
    set(state => ({
      tasks: {
        ...state.tasks,
        [taskId]: {
          ...state.tasks[taskId],
          sseEventSource: null,
          activeEvaluationPollIntervalId: undefined,
          activeGenerationPollIntervalId: undefined,
          isStreaming: false,
          bufferFlushTimer: null,
          chunkBuffer: {},
          lastFlushTime: 0,
        }
      }
    }));
  },
  
  stopPollingGenerationStatus: (taskId: number) => {
    const taskState = get().tasks[taskId];
    if (taskState?.activeGenerationPollIntervalId) {
      clearInterval(taskState.activeGenerationPollIntervalId);
      set(s => ({ tasks: { ...s.tasks, [taskId]: { ...s.tasks[taskId], activeGenerationPollIntervalId: undefined }}}));
      console.log(`[PollingGen] Stopped generation polling for task ${taskId}`);
    }
  },

  startPollingGenerationStatus: (taskId: number) => {
    const taskData = get().tasks[taskId];
    if (!taskData) {
        console.warn(`[PollingGen] Task ${taskId} not found. Cannot start polling.`);
        return;
    }
    if (taskData.activeGenerationPollIntervalId) {
        console.log(`[PollingGen] Task ${taskId} generation poll already active.`);
        return;
    }
    // Do not poll if task is already in a final state
    if (taskData.taskStatusFromBackend === 'COMPLETED' || taskData.taskStatusFromBackend === 'FAILED') {
        console.log(`[PollingGen] Task ${taskId} is already ${taskData.taskStatusFromBackend}. Not starting poll.`);
        return;
    }

    console.log(`[PollingGen] Starting generation polling for task ${taskId}`);
    let consecutiveErrors = 0;

    const intervalId = setInterval(async () => {
      const currentTaskStateForPoll = get().tasks[taskId]; // Get latest state inside interval
      if (!currentTaskStateForPoll || currentTaskStateForPoll.activeGenerationPollIntervalId !== intervalId) {
        clearInterval(intervalId); 
        console.log(`[PollingGen] Task ${taskId} state changed or poll superseded. Stopping this poll instance.`);
        return;
      }

      try {
        const details: TaskStatusResponse = await api.getTaskDetails(taskId);
        consecutiveErrors = 0;

        set(state => {
          const task = state.tasks[taskId];
          if (!task) return state; 
          // The linter error was around here. Ensure `state` is from the `set` callback.
          // The following correctly uses `task` which is derived from `state.tasks[taskId]`. 
          return { 
            tasks: { 
              ...state.tasks, 
              [taskId]: {
            ...task,
                outputs: details.generations?.map(g => { // Merge reasoning_text with existing one if backend lacks it
                  const existingGen = task.outputs.find(o => o.id === g.id || o.model_id_used === g.model_id_used);
                  return {
                    ...g,
                    reasoning_text: g.reasoning_text ?? existingGen?.reasoning_text ?? null,
                  };
                }) || [], 
            taskStatusFromBackend: details.status,
            isGenerating: details.status === 'GENERATING',
            errorMessage: details.status === 'FAILED' ? (details.generations?.find(g => g.error_message)?.error_message || task.errorMessage || 'Generation failed') : task.errorMessage,
              }
            }
          };
        });

        // Access the updated state for decision making after set has (notionally) completed
        const updatedTaskState = get().tasks[taskId]; 
        if (updatedTaskState) { 
            if (updatedTaskState.taskStatusFromBackend === 'COMPLETED' || updatedTaskState.taskStatusFromBackend === 'FAILED') {
              console.log(`[PollingGen] Task ${taskId} reached final state: ${updatedTaskState.taskStatusFromBackend} via poll. Stopping poll.`);
              get().stopPollingGenerationStatus(taskId); 
          await get().loadTaskHistory(); 
            } else if (updatedTaskState.taskStatusFromBackend === 'GENERATING') {
           // Still generating, continue polling.
            } else if (updatedTaskState.taskStatusFromBackend === 'PENDING') {
            // Still pending, continue polling.
        } else {
              console.warn(`[PollingGen] Task ${taskId} in unexpected state ${updatedTaskState.taskStatusFromBackend} during generation poll. Stopping.`);
            get().stopPollingGenerationStatus(taskId);
            await get().loadTaskHistory();
        }
        } // End if (updatedTaskState)

      } catch (err: unknown) {
        consecutiveErrors++;
        console.error(`[PollingGen] Error polling gen status for task ${taskId} (Attempt ${consecutiveErrors}):`, err);
        if (consecutiveErrors >= MAX_GENERATION_POLL_ERRORS) {
          console.error(`[PollingGen] Max polling errors for task ${taskId}. Stopping poll.`);
          get().stopPollingGenerationStatus(taskId);
          set(state => ({ // Correctly use state here for the error update
            tasks: { 
              ...state.tasks, 
              [taskId]: {
                ...state.tasks[taskId],
                errorMessage: state.tasks[taskId]?.errorMessage || `Polling failed after ${MAX_GENERATION_POLL_ERRORS} attempts.`,
            isGenerating: false, 
              }
            }
          }));
          await get().loadTaskHistory();
        }
      }
    }, GENERATION_POLL_INTERVAL);

    set(state => ({ tasks: { ...state.tasks, [taskId]: { ...state.tasks[taskId], activeGenerationPollIntervalId: intervalId }}}));
  },

  startPollingTaskHistory: () => {
    if (get().activeHistoryPollIntervalId) {
      // console.log('[PollingHist] History polling already active.');
      return;
    }
    console.log('[PollingHist] Starting task history polling.');

    const intervalId = setInterval(async () => {
      // console.log('[PollingHist] Polling task history...');
      await get().loadTaskHistory(); 
      // loadTaskHistory itself will check if polling needs to continue or stop
      // So, no need to check statuses and stop here explicitly, unless loadTaskHistory fails
      // However, we must ensure this interval instance is cleared if stopPollingTaskHistory is called from elsewhere or by loadTaskHistory
      if (get().activeHistoryPollIntervalId !== intervalId) {
          clearInterval(intervalId);
          console.log('[PollingHist] History poll superseded or stopped by another action. Clearing this interval.');
      }

    }, HISTORY_POLL_INTERVAL);
    set({ activeHistoryPollIntervalId: intervalId });
  },

  stopPollingTaskHistory: () => {
    const currentIntervalId = get().activeHistoryPollIntervalId;
    if (currentIntervalId) {
      clearInterval(currentIntervalId);
      set({ activeHistoryPollIntervalId: null });
      console.log('[PollingHist] Stopped task history polling.');
    }
  },

  flushChunkBuffer: (taskId: number) => {
    const taskState = get().tasks[taskId];
    if (!taskState || Object.keys(taskState.chunkBuffer).length === 0) return;
    
    console.log(`[FlushBuffer] Flushing chunk buffer for task ${taskId}, models:`, Object.keys(taskState.chunkBuffer));
    
    set(state => {
      const currentTask = state.tasks[taskId];
      if (!currentTask) return state;

      const newOutputs = currentTask.outputs.map(output => {
        const bufferedData = currentTask.chunkBuffer[output.model_id_used];
        if (bufferedData) {
          return {
            ...output,
            output_text: (output.output_text || '') + bufferedData.output_text,
            reasoning_text: (output.reasoning_text || '') + bufferedData.reasoning_text
          };
        }
        return output;
      });

      // Handle new models that don't exist in outputs yet
      Object.keys(currentTask.chunkBuffer).forEach(modelId => {
        const existsInOutputs = newOutputs.some(o => o.model_id_used === modelId);
        if (!existsInOutputs) {
          const bufferedData = currentTask.chunkBuffer[modelId];
          newOutputs.push({
            id: Date.now() + Math.random(), // Ensure unique ID
            task_id: taskId,
            model_id_used: modelId,
            blind_id: `temp-blind-msg-${modelId}-${Date.now()}`,
            output_text: bufferedData.output_text,
            reasoning_text: bufferedData.reasoning_text,
            error_message: null,
            created_at: new Date().toISOString()
          });
        }
      });

      return {
        tasks: {
          ...state.tasks,
          [taskId]: {
            ...currentTask,
            outputs: newOutputs,
            chunkBuffer: {}, // Clear buffer after flush
            bufferFlushTimer: null,
            lastFlushTime: Date.now(), // Record flush time
          }
        }
      };
    });
  },

    }),
    {
      name: 'llm-eval-task-store', // Name for localStorage key
      storage: createJSONStorage(() => localStorage), // Use localStorage
      partialize: (state) => ({
        // Only persist serializable task data, exclude runtime state
        tasks: Object.fromEntries(
          Object.entries(state.tasks).map(([taskId, task]) => [
            taskId,
            {
              id: task.id,
              prompt: task.prompt,
              system_prompt: task.system_prompt,
              requestedModels: task.requestedModels,
              outputs: task.outputs,
              currentEvaluationId: task.currentEvaluationId,
              currentReport: task.currentReport,
              evaluations: task.evaluations,
              cacheTimestamp: task.cacheTimestamp,
              taskStatusFromBackend: task.taskStatusFromBackend,
              aggregatedReportsByAlgorithm: task.aggregatedReportsByAlgorithm,
              errorMessage: task.errorMessage,
              // Exclude runtime state: don't persist sseEventSource, isStreaming, isGenerating, isEvaluating, isAggregating,
              // modelsDoneStreamingThisTask, activeEvaluationPollIntervalId, activeGenerationPollIntervalId, aggregationError
              // These will be re-initialized based on taskStatusFromBackend when page reloads
              isGenerating: false,
              isStreaming: false,
              isEvaluating: false,
              isAggregating: false,
              sseEventSource: null,
              modelsDoneStreamingThisTask: new Set<string>(),
              aggregationError: null,
              chunkBuffer: {},
              bufferFlushTimer: null,
              lastFlushTime: 0,
            }
          ])
        ),
        // selectedTaskId: state.selectedTaskId // Optionally persist selectedTaskId if needed across sessions
      }),
    }
  )
);

export default useTaskStore; 