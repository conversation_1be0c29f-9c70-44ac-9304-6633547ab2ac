import React, { useRef, useState } from 'react';
import LoadingSpinner from './LoadingSpinner';
import SingleOutputDisplay from './SingleOutputDisplay';
import { Generation } from '../api/apiClient';
import { Resizable } from 're-resizable';

interface OutputDisplayProps {
  selectedGeneration: Generation | null;
  isLoading: boolean;
  showScrollbar?: boolean;
}

const OutputDisplay: React.FC<OutputDisplayProps> = ({ 
  selectedGeneration, 
  isLoading, 
  showScrollbar = true 
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isHovering, setIsHovering] = useState(false);

  if (isLoading && !selectedGeneration) {
    return (
      <div className="flex justify-center py-12">
        <LoadingSpinner message="Generating responses..." size="large" />
      </div>
    );
  }

  if (!selectedGeneration) {
    return (
      <div className="bg-light-component-subtle dark:bg-dark-component-subtle p-6 rounded-lg border border-light-border dark:border-dark-border text-center">
        <p className="text-light-secondary dark:text-dark-secondary py-8">
          No outputs generated yet. Please wait for model responses.
        </p>
      </div>
    );
  }

  // Show all mode: don't use Resizable, display content directly
  if (!showScrollbar) {
    return (
      <div className="rounded-xl border border-light-border/60 dark:border-dark-border/60 bg-gradient-to-br from-light-component to-light-background dark:from-dark-component dark:to-dark-background shadow-sm">
        <div 
          ref={containerRef} 
          className="p-6 sm:p-8 text-light-primary dark:text-dark-primary"
        >
          {selectedGeneration && (
            <SingleOutputDisplay generation={selectedGeneration} />
          )}
        </div>
      </div>
    );
  }

  return (
      <Resizable
        defaultSize={{
          width: '100%',
          height: 500,
        }}
        minHeight={300}
        enable={{
          top: false,
          right: false,
          bottom: true,
          left: false,
          topRight: false,
          bottomRight: false,
          bottomLeft: false,
          topLeft: false,
        }}
        className="rounded-xl border border-light-border/60 dark:border-dark-border/60 bg-gradient-to-br from-light-component to-light-background dark:from-dark-component dark:to-dark-background shadow-sm overflow-hidden"
        handleStyles={{
          bottom: {
            bottom: 0,
            height: '8px',
            cursor: 'ns-resize',
          }
        }}
        handleClasses={{
        bottom: `transition-all duration-200 ${
          isHovering 
            ? 'bg-light-accent/40 dark:bg-dark-accent/40 shadow-sm' 
            : 'bg-transparent hover:bg-light-accent/20 dark:hover:bg-dark-accent/20'
        }`
        }}
      >
        <div 
          ref={containerRef} 
          className="h-full overflow-auto p-6 sm:p-8 text-light-primary dark:text-dark-primary custom-scrollbar"
        onMouseEnter={() => setIsHovering(true)}
        onMouseLeave={() => setIsHovering(false)}
        >
          {selectedGeneration && (
            <SingleOutputDisplay generation={selectedGeneration} />
          )}
        </div>
      </Resizable>
  );
};

export default OutputDisplay; 