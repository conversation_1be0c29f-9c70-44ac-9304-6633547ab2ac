import React from 'react';

interface LoadingSpinnerProps {
  message?: string;
  size?: 'small' | 'medium' | 'large';
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({ 
  message = 'Loading...', 
  size = 'medium' 
}) => {
  const sizeClasses = {
    small: 'h-6 w-6',
    medium: 'h-10 w-10',
    large: 'h-16 w-16',
  };

  return (
    <div className="flex flex-col items-center justify-center space-y-6">
      {/* Single clean spinner */}
      <div className="relative">
        <div className={`${sizeClasses[size]} border-3 border-light-component-subtle dark:border-dark-component-subtle border-t-light-accent dark:border-t-dark-accent rounded-full animate-spin`} />
      </div>
      
      {message && (
        <div className="text-center space-y-3">
          <p className="text-sm font-medium text-light-primary dark:text-dark-primary">
            {message}
          </p>
          {/* Simple pulsing dots below text */}
          <div className="flex justify-center space-x-1.5">
            <div className="w-2 h-2 bg-light-accent dark:bg-dark-accent rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
            <div className="w-2 h-2 bg-light-accent dark:bg-dark-accent rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
            <div className="w-2 h-2 bg-light-accent dark:bg-dark-accent rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
          </div>
        </div>
      )}
    </div>
  );
};

export default LoadingSpinner; 