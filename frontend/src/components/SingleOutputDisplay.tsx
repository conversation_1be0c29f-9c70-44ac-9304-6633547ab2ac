import React, { useState, HTMLAttributes } from 'react';
import ReactMarkdown from 'react-markdown';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { vscDarkPlus, vs } from 'react-syntax-highlighter/dist/esm/styles/prism';
import rehypeRaw from 'rehype-raw';
import remarkBreaks from 'remark-breaks';
import { Generation } from '../api/apiClient';
import { useTheme } from '../context/ThemeContext';
import { Check, Copy as CopyIcon, Sparkles, ChevronUp, ChevronDown } from 'lucide-react';

/* eslint-disable @typescript-eslint/no-explicit-any */

interface SingleOutputDisplayProps {
  generation: Generation;
}

// // Get model display name
// const getModelDisplayName = (modelId: string) => {
//   const parts = modelId.split('/');
//   return parts.length > 1 ? parts[1] : modelId;
// };

// // Get provider name from model ID
// const getProviderName = (modelId: string) => {
//   const parts = modelId.split('/');
//   return parts.length > 1 ? parts[0] : '';
// };

interface CustomCodeProps {
  node?: unknown;
  inline?: boolean;
  className?: string;
  children?: React.ReactNode;
  [key: string]: any; // For other props passed by SyntaxHighlighter or custom attributes
}

// For props passed to paragraph elements by ReactMarkdown
interface ParagraphProps extends HTMLAttributes<HTMLParagraphElement> {
  children?: React.ReactNode;
  node?: unknown; // Provided by ReactMarkdown, mark as unknown if not used, or use specific type from 'unist' if needed.
}

// Define a more specific type for code component props used
interface CustomReactMarkdownCodeProps {
  node?: unknown; // from react-markdown
  inline?: boolean;
  className?: string;
  children?: React.ReactNode;
  [key: string]: any; // For other props passed by SyntaxHighlighter or custom attributes
}

const CustomCodeRenderer: React.FC<CustomCodeProps> = ({ inline, className, children, ...props }) => {
  const { resolvedTheme } = useTheme();
  const [copied, setCopied] = useState(false);
  const match = /language-(\w+)/.exec(className || '');
  const lang = match ? match[1] : 'text';

  const handleCopy = () => {
    const codeToCopy = String(children).replace(/\n$/, '');
    navigator.clipboard.writeText(codeToCopy);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  const syntaxTheme = resolvedTheme === 'dark' ? vscDarkPlus : vs;

  // Handle inline code and single-line no-language blocks
  if (inline || (!match && typeof children === 'string' && !children.includes('\n'))) {
    return (
      <code 
        {...props} 
        className="bg-light-component dark:bg-dark-component-subtle border border-light-border dark:border-dark-border text-light-accent dark:text-dark-accent px-1.5 py-0.5 mx-0.5 rounded font-mono text-[0.9em] font-medium transition-colors duration-300"
      >
        {children}
      </code>
    );
  }

  if (!inline && match) {
    return (
      <div className="code-block-container relative group bg-light-background dark:bg-dark-component-subtle rounded-md my-2 shadow-md border border-light-border dark:border-dark-border rounded-t-md">
        <div className="code-block-header flex justify-between items-center px-3 py-1.5 bg-light-component dark:bg-dark-component border-b border-light-border dark:border-dark-border rounded-t-md">
          <span className="text-xs text-light-secondary dark:text-dark-secondary font-mono select-none">{lang}</span>
          <button 
            onClick={handleCopy}
            className="text-xs text-light-secondary dark:text-dark-secondary hover:text-light-primary dark:hover:text-dark-primary p-1 rounded opacity-60 group-hover:opacity-100"
            title="Copy code"
          >
            {copied ? <Check size={16} className="text-green-500" /> : <CopyIcon size={16} />}
          </button>
        </div>
        <SyntaxHighlighter
          {...props}
          style={syntaxTheme}
          language={lang}
          PreTag="div"
          className="!p-3 !m-0 !bg-transparent !text-sm overflow-x-auto rounded-b-md custom-scrollbar"
        >
          {String(children).replace(/\n$/, '')}
        </SyntaxHighlighter>
      </div>
    );
  } 
  
  if (!inline) {
    // Fallback for code blocks without a language
    return (
      <div className="code-block-container relative group bg-light-background dark:bg-dark-component-subtle border border-light-border dark:border-dark-border rounded-md my-2 shadow">
         <div className="code-block-header flex justify-end items-center px-3 py-1.5 bg-light-component dark:bg-dark-component border-b border-light-border dark:border-dark-border rounded-t-md">
          <button 
            onClick={handleCopy}
            className="text-xs text-light-secondary dark:text-dark-secondary hover:text-light-primary dark:hover:text-dark-primary p-1 rounded opacity-60 group-hover:opacity-100"
            title="Copy code"
          >
            {copied ? <Check size={16} className="text-green-500" /> : <CopyIcon size={16} />}
          </button>
        </div>
        <pre className="!p-3 !m-0 !bg-transparent text-sm overflow-x-auto rounded-b-md custom-scrollbar whitespace-pre-wrap text-light-primary dark:text-dark-primary">
          <code {...props} className={className}>
            {children}
          </code>
        </pre>
      </div>
    );
  }

  // For inline code
  return (
    <code {...props} className={`${className || ''} bg-light-component dark:bg-dark-component-subtle border border-light-border dark:border-dark-border text-light-accent dark:text-dark-accent px-1.5 py-0.5 mx-0.5 rounded font-mono text-[0.9em] font-medium transition-colors duration-300`}>
      {children}
    </code>
  );
};

const SingleOutputDisplay: React.FC<SingleOutputDisplayProps> = React.memo(({ generation }) => {
  const [isReasoningVisible, setIsReasoningVisible] = useState(true);

  // Custom paragraph renderer to prevent nesting block elements inside <p>
  const CustomParagraphRenderer: React.FC<ParagraphProps> = ({ children, node: _node, ...props }) => {
    // Do not wrap any block-level React elements in <p>
    const blockTags = ['div', 'pre', 'ul', 'ol', 'li', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'table', 'blockquote', 'figure'];
    const childArray = React.Children.toArray(children);
    const hasBlockChild = childArray.some(child => {
      if (!React.isValidElement(child)) return false;
      if (typeof child.type === 'string') {
        return blockTags.includes(child.type);
      }
      // If the child renders a block-level container like our CustomCodeRenderer
      return child.type === CustomCodeRenderer;
    });
    if (hasBlockChild) {
      return <>{children}</>;
    }
    return <p {...props}>{children}</p>;
  };
  
  return (
    <div className="h-full text-light-primary dark:text-dark-primary">
      {generation.reasoning_text && (
        <div className="mb-4 pb-4">
          <button 
            className="flex items-center justify-between w-full text-left py-2 px-4 rounded-md bg-light-component dark:bg-dark-component border border-light-border dark:border-dark-border shadow-sm hover:bg-light-hover dark:hover:bg-dark-hover hover:shadow focus:outline-none focus-visible:ring focus-visible:ring-light-accent focus-visible:ring-opacity-75"
            onClick={() => setIsReasoningVisible(!isReasoningVisible)}
            aria-expanded={isReasoningVisible}
            aria-controls="reasoning-content"
          >
            <div className="flex items-center">
              <Sparkles className="mr-2 text-light-accent dark:text-dark-accent" size={16} />
              <h4 className="text-sm font-medium text-light-primary dark:text-dark-primary">
                {isReasoningVisible ? "Hide Reasoning Process" : "Show Reasoning Process"}
              </h4>
            </div>
            {isReasoningVisible ? (
              <ChevronUp className="text-light-secondary dark:text-dark-secondary group-hover:text-light-primary dark:group-hover:text-dark-primary" size={16} />
            ) : (
              <ChevronDown className="text-light-secondary dark:text-dark-secondary group-hover:text-light-primary dark:group-hover:text-dark-primary" size={16} />
            )}
          </button>
          {isReasoningVisible && (
            <div id="reasoning-content" className="mt-2 prose prose-sm dark:prose-dark max-w-none">
              <div className="p-3 bg-light-component-subtle dark:bg-dark-component-subtle border border-light-border dark:border-dark-border rounded-md transition-colors duration-300">
                <ReactMarkdown
                  {...{
                    rehypePlugins: [rehypeRaw],
                    remarkPlugins: [remarkBreaks],
                    components: {
                      code: ({ node: _codeNode, inline, className, children, ...props }: CustomReactMarkdownCodeProps) => {
                        if (inline) {
                          return (
                            <code
                              {...props}
                              className="bg-light-component dark:bg-dark-component-subtle border border-light-border dark:border-dark-border text-light-accent dark:text-dark-accent px-1.5 py-0.5 rounded-sm text-sm font-mono mx-0.5"
                            >
                              {children}
                            </code>
                          );
                        }
                        return <CustomCodeRenderer inline={false} className={className} {...props}>{children}</CustomCodeRenderer>;
                      },
                      p: CustomParagraphRenderer
                    }
                  } as any}
                >
                  {generation.reasoning_text.replace(/\\n/g, '\n')}
                </ReactMarkdown>
              </div>
            </div>
          )}
        </div>
      )}
      
      {generation.error_message ? (
        <div className="text-light-error dark:text-dark-error bg-light-error-bg dark:bg-dark-error-bg p-3 rounded-md border border-red-200 dark:border-red-700/50 transition-colors duration-300">
          <p className="font-medium mb-1">Error:</p>
          <pre className="whitespace-pre-wrap text-sm">{generation.error_message}</pre>
        </div>
      ) : (
        <div className="prose prose-sm dark:prose-dark max-w-none">
          <ReactMarkdown
            {...{
              rehypePlugins: [rehypeRaw],
              remarkPlugins: [remarkBreaks],
              components: {
                code: ({ node: _codeNode, inline, className, children, ...props }: CustomReactMarkdownCodeProps) => {
                  if (inline) {
                    return (
                      <code
                        {...props}
                        className="bg-light-component dark:bg-dark-component-subtle border border-light-border dark:border-dark-border text-light-accent dark:text-dark-accent px-1.5 py-0.5 rounded-sm text-sm font-mono mx-0.5"
                      >
                        {children}
                      </code>
                    );
                  }
                  return <CustomCodeRenderer inline={false} className={className} {...props}>{children}</CustomCodeRenderer>;
                },
                p: CustomParagraphRenderer
              }
            } as any}
          >
            {generation.output_text || ''}
          </ReactMarkdown>
        </div>
      )}
    </div>
  );
});

export default SingleOutputDisplay; 