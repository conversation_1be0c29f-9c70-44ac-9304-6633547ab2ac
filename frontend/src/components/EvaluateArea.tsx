import { useState } from 'react';
import ModelSelector from './ModelSelector';

interface EvaluateAreaProps {
  availableModels: string[];
  onEvaluate: (selectedModels: string[], useBlindIds: boolean, customEvaluationPrompt?: string) => void;
  isLoading?: boolean;
  disabled?: boolean;
  validOutputsCount?: number;
}

const EvaluateArea: React.FC<EvaluateAreaProps> = ({
  availableModels,
  onEvaluate,
  isLoading = false,
  disabled = false,
  validOutputsCount = 0
}) => {
  const [selectedEvaluators, setSelectedEvaluators] = useState<string[]>([]);
  const [useBlindIdsForEval, setUseBlindIdsForEval] = useState<boolean>(true);
  const [customEvaluationPrompt, setCustomEvaluationPrompt] = useState<string>('');
  const [useCustomPrompt, setUseCustomPrompt] = useState<boolean>(false);
  const [showDefaultPrompt, setShowDefaultPrompt] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const hasInsufficientOutputs = validOutputsCount < 2;
  const canEvaluate = selectedEvaluators.length > 0 && !hasInsufficientOutputs;

  const defaultEvaluationPrompt = `Evaluate the quality of the generated outputs based on the original task prompt. Consider relevance, completeness, coherence, and accuracy.`;

  const handleEvaluateClick = () => {
    if (hasInsufficientOutputs) {
      setError('Need at least 2 valid outputs to perform evaluation.');
      return;
    }
    if (selectedEvaluators.length === 0) {
      setError('Please select at least one evaluator model.');
      return;
    }
    setError(null);
    onEvaluate(selectedEvaluators, useBlindIdsForEval, useCustomPrompt ? customEvaluationPrompt : undefined);
  };

  return (
    <div className="space-y-4">
      {hasInsufficientOutputs && (
        <div className="p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
          <div className="flex items-center">
            <svg className="w-5 h-5 text-yellow-600 dark:text-yellow-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.98-.833-2.75 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            <p className="text-sm text-yellow-700 dark:text-yellow-300">
              Need at least 2 valid outputs to perform evaluation. Currently have {validOutputsCount} valid output{validOutputsCount === 1 ? '' : 's'}.
            </p>
          </div>
        </div>
      )}
      
      {!hasInsufficientOutputs && (
        <>
          <ModelSelector
            title="Select Evaluator Models"
            availableModels={availableModels}
            onSelectionChange={(models) => {
              setSelectedEvaluators(models);
              if (error && models.length > 0 && !hasInsufficientOutputs) {
                setError(null);
              }
            }}
            disabled={isLoading || disabled}
          />
          
          <div className="flex items-center space-x-2 pt-1">
            <input 
              type="checkbox" 
              id="useBlindIdsEval" 
              checked={useBlindIdsForEval} 
              onChange={(e) => setUseBlindIdsForEval(e.target.checked)}
              disabled={isLoading || disabled}
              className="h-4 w-4 text-light-accent dark:text-dark-accent bg-light-component dark:bg-dark-component border-light-border dark:border-dark-border rounded focus:ring-light-accent dark:focus:ring-dark-accent transition-colors duration-300"
            />
            <label htmlFor="useBlindIdsEval" className="text-sm select-none transition-colors duration-300 text-light-text dark:text-dark-text">
              Use Blind IDs for this Evaluation
            </label>
          </div>
          
          <div className="flex items-center space-x-2 pt-1">
            <input 
              type="checkbox" 
              id="useCustomPrompt" 
              checked={useCustomPrompt} 
              onChange={(e) => setUseCustomPrompt(e.target.checked)}
              disabled={isLoading || disabled}
              className="h-4 w-4 text-light-accent dark:text-dark-accent bg-light-component dark:bg-dark-component border-light-border dark:border-dark-border rounded focus:ring-light-accent dark:focus:ring-dark-accent transition-colors duration-300"
            />
            <label htmlFor="useCustomPrompt" className="text-sm select-none transition-colors duration-300 text-light-text dark:text-dark-text">
              Use Custom Evaluation Prompt
            </label>
            <button
              type="button"
              onClick={() => setShowDefaultPrompt(!showDefaultPrompt)}
              className="text-xs text-light-accent dark:text-dark-accent hover:text-light-accent-hover dark:hover:text-dark-accent-hover underline transition-colors duration-300"
              disabled={isLoading || disabled}
            >
              {showDefaultPrompt ? 'Hide' : 'Show'} Default Prompt
            </button>
          </div>
          
          {showDefaultPrompt && (
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <label className="block text-sm font-medium text-light-text dark:text-dark-text">
                  Default Evaluation Prompt
                </label>
                <button
                  onClick={() => {
                    setCustomEvaluationPrompt(defaultEvaluationPrompt);
                    setUseCustomPrompt(true);
                  }}
                  disabled={isLoading || disabled}
                  className="text-xs px-2 py-1 bg-light-accent dark:bg-dark-accent text-white rounded hover:bg-light-accent-hover dark:hover:bg-dark-accent-hover transition-colors duration-300 disabled:opacity-50"
                >
                  Copy to Custom
                </button>
              </div>
              <div className="text-sm text-light-text dark:text-dark-text bg-light-component dark:bg-dark-component p-3 rounded border border-light-border dark:border-dark-border">
                {defaultEvaluationPrompt}
              </div>
            </div>
          )}
          
          {useCustomPrompt && (
            <div className="space-y-2">
              <label htmlFor="customPrompt" className="block text-sm font-medium text-light-text dark:text-dark-text">
                Custom Evaluation Instructions
              </label>
              <textarea
                id="customPrompt"
                value={customEvaluationPrompt}
                onChange={(e) => setCustomEvaluationPrompt(e.target.value)}
                disabled={isLoading || disabled}
                placeholder="Enter your custom evaluation instructions..."
                rows={6}
                className="w-full px-3 py-2 border border-light-border dark:border-dark-border rounded-md bg-light-component dark:bg-dark-component text-light-text dark:text-dark-text placeholder-light-secondary dark:placeholder-dark-secondary focus:outline-none focus:ring-2 focus:ring-light-accent dark:focus:ring-dark-accent focus:border-transparent transition-colors duration-300 resize-vertical"
              />
              <div className="flex justify-end">
                <button
                  onClick={() => setCustomEvaluationPrompt('')}
                  disabled={isLoading || disabled || !customEvaluationPrompt}
                  className="text-xs text-light-secondary dark:text-dark-secondary hover:text-light-primary dark:hover:text-dark-primary transition-colors duration-300 disabled:opacity-50"
                >
                  Clear
                </button>
              </div>
            </div>
          )}
          
          {error && (
            <p className="text-sm text-light-error dark:text-dark-error transition-colors duration-300">{error}</p>
          )}
          
          <div className="flex justify-end pt-2">
            <button
              onClick={handleEvaluateClick}
              disabled={isLoading || disabled || !canEvaluate}
              className={`px-4 py-2 rounded-md text-white font-medium text-sm transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-offset-dark-component-subtle
                ${
                  isLoading || disabled || !canEvaluate
                    ? 'bg-light-secondary/70 dark:bg-dark-secondary/70 cursor-not-allowed'
                    : 'bg-light-accent dark:bg-dark-accent hover:bg-light-accent-hover dark:hover:bg-dark-accent-hover focus:ring-light-accent dark:focus:ring-dark-accent'
                }`}
            >
              {isLoading ? 'Evaluating...' : 'Generate Evaluation Report'}
            </button>
          </div>
        </>
      )}
    </div>
  );
};

export default EvaluateArea; 