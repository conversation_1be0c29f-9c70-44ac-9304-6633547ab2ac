import React, { useState } from 'react';
import ReactMarkdown from 'react-markdown';
import rehypeRaw from 'rehype-raw';
import { Copy as CopyIcon, Check, ChevronDown, ChevronRight } from 'lucide-react';

// Define a more specific type for code component props used by ReactMarkdown
interface CustomReactMarkdownCodeProps {
  node?: unknown; // from react-markdown
  inline?: boolean;
  className?: string;
  children?: React.ReactNode;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [key: string]: any; // For other props passed by SyntaxHighlighter or custom attributes
}

interface PromptDisplayProps {
  title: string;
  content: string;
  defaultContent?: string;
  showCopyButton?: boolean;
  customCodeRenderer?: React.ComponentType<CustomReactMarkdownCodeProps>;
  defaultExpanded?: boolean;
}

const PromptDisplay: React.FC<PromptDisplayProps> = ({
  title,
  content,
  defaultContent,
  showCopyButton = true,
  customCodeRenderer,
  defaultExpanded = true
}) => {
  const [isCopied, setIsCopied] = useState(false);
  const [isExpanded, setIsExpanded] = useState(defaultExpanded);

  const handleCopy = () => {
    const textToCopy = content || defaultContent || '';
    navigator.clipboard.writeText(textToCopy);
    setIsCopied(true);
    setTimeout(() => setIsCopied(false), 2000);
  };

  const displayContent = content || defaultContent || '';
  const isDefault = !content && defaultContent;

  return (
    <div className="mb-4">
      <div className="flex justify-between items-center mb-3">
        <button
          onClick={() => setIsExpanded(!isExpanded)}
          className="flex items-center text-left hover:text-light-primary dark:hover:text-dark-primary transition-colors duration-200"
        >
          {isExpanded ? (
            <ChevronDown className="w-4 h-4 mr-2 text-light-accent dark:text-dark-accent" />
          ) : (
            <ChevronRight className="w-4 h-4 mr-2 text-light-accent dark:text-dark-accent" />
          )}
          <h3 className="text-xs font-semibold text-light-secondary dark:text-dark-secondary uppercase tracking-wider">
            {title}
            {isDefault && (
              <span className="ml-2 text-xs font-normal text-light-secondary dark:text-dark-secondary normal-case">
                (Default)
              </span>
            )}
          </h3>
        </button>
        {showCopyButton && isExpanded && (
          <button
            onClick={handleCopy}
            className="group text-light-secondary dark:text-dark-secondary hover:text-light-primary dark:hover:text-dark-primary p-1 rounded-md hover:bg-light-component-subtle dark:hover:bg-dark-component-subtle transition-colors duration-200"
            title={`Copy ${title.toLowerCase()}`}
          >
            {isCopied ? (
              <Check size={14} className="text-green-500" />
            ) : (
              <CopyIcon size={14} />
            )}
          </button>
        )}
      </div>
      {isExpanded && (
        <div className="prose prose-sm dark:prose-dark max-w-none text-light-primary dark:text-dark-primary bg-gradient-to-br from-light-component to-light-background dark:from-dark-component dark:to-dark-background p-3 rounded-md border border-light-border/60 dark:border-dark-border/60 shadow-sm transition-all duration-200">
          <ReactMarkdown
            rehypePlugins={[rehypeRaw]}
            components={customCodeRenderer ? { code: customCodeRenderer } : undefined}
          >
            {displayContent}
          </ReactMarkdown>
        </div>
      )}
    </div>
  );
};

export default PromptDisplay; 