import React, { useState, useEffect } from 'react';
import { Bot, ChevronDown, ChevronRight } from 'lucide-react';
import PromptDisplay from './PromptDisplay';

interface PromptInputProps {
  onSubmit: (prompt: string, selectedModels: string[], systemPrompt?: string) => void;
  isLoading: boolean;
  availableModels: string[];
  initialPrompt?: string; // Optional initial prompt text
  initialSystemPrompt?: string; // Optional initial system prompt text
  isReadOnly?: boolean; // Add this prop to indicate if viewing historical task
  initialSelectedModels?: string[]; // Add this prop for historical models
}

const PromptInput: React.FC<PromptInputProps> = ({ 
  onSubmit, 
  isLoading, 
  availableModels, 
  initialPrompt = '', 
  initialSystemPrompt = '', 
  isReadOnly = false, // Default to editable
  initialSelectedModels = [] // Default to empty array
}) => {
  const [prompt, setPrompt] = useState(initialPrompt);
  const [selectedModels, setSelectedModels] = useState<string[]>(initialSelectedModels);
  const [systemPrompt, setSystemPrompt] = useState(initialSystemPrompt);
  const [isSystemPromptExpanded, setIsSystemPromptExpanded] = useState(false);
  
  useEffect(() => {
    // Update prompt when initialPrompt changes (e.g., when viewing a historical task)
    setPrompt(initialPrompt);
  }, [initialPrompt]);

  useEffect(() => {
    // Update system prompt when initialSystemPrompt changes
    setSystemPrompt(initialSystemPrompt);
  }, [initialSystemPrompt]);

  useEffect(() => {
    // Update selected models when initialSelectedModels changes
    if (initialSelectedModels && initialSelectedModels.length > 0) {
      setSelectedModels(initialSelectedModels);
    }
  }, [initialSelectedModels]);
  
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (prompt.trim() && selectedModels.length > 0 && !isLoading) {
      onSubmit(prompt, selectedModels, systemPrompt);
    }
  };
  
  const handleModelChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const model = e.target.value;
    setSelectedModels(prev => 
      e.target.checked 
        ? [...prev, model] 
        : prev.filter(m => m !== model)
    );
  };
  
  // Get display name from model ID
  const getModelDisplayName = (modelId: string) => {
    const parts = modelId.split('/');
    return parts.length > 1 ? parts[1] : modelId;
  };
  
  // Get provider name from model ID
  const getProviderName = (modelId: string) => {
    const parts = modelId.split('/');
    return parts.length > 1 ? parts[0] : '';
  };
  
  // Render read-only view for historical tasks
  if (isReadOnly) {
    return (
      <div className="space-y-4">
        <PromptDisplay
          title="System Prompt"
          content={systemPrompt}
          defaultContent="You are a helpful assistant. Please provide clear, accurate, and well-structured responses."
          showCopyButton={false}
          defaultExpanded={false}
        />
        
        <PromptDisplay
          title="User Prompt"
          content={prompt}
          showCopyButton={false}
          defaultExpanded={true}
        />
        
        <div>
          <h4 className="text-xs uppercase tracking-wide text-light-secondary dark:text-dark-secondary mb-2 font-medium transition-colors duration-300">Models Used</h4>
          <div className="flex flex-wrap gap-2">
            {selectedModels.map(model => (
              <span 
                key={model}
                className="inline-flex items-center px-2.5 py-0.5 rounded-md text-xs font-medium 
                          border border-light-border/50 dark:border-dark-border/50 text-light-primary dark:text-dark-primary transition-colors duration-300"
              >
                <Bot className="mr-1" size={12} />
                {getModelDisplayName(model)}
              </span>
            ))}
          </div>
        </div>
      </div>
    );
  }
  
  // Editable interface
  return (
    <form onSubmit={handleSubmit} className="space-y-5">
      <div>
        <button
          type="button"
          onClick={() => setIsSystemPromptExpanded(!isSystemPromptExpanded)}
          className="flex items-center w-full text-left mb-2 text-xs uppercase tracking-wide text-light-secondary dark:text-dark-secondary font-medium transition-colors duration-300 hover:text-light-primary dark:hover:text-dark-primary"
        >
          {isSystemPromptExpanded ? (
            <ChevronDown className="w-3 h-3 mr-1" />
          ) : (
            <ChevronRight className="w-3 h-3 mr-1" />
          )}
          System Prompt (Optional)
        </button>
        {isSystemPromptExpanded && (
          <textarea
            value={systemPrompt}
            onChange={(e) => setSystemPrompt(e.target.value)}
            placeholder="Enter system prompt here (optional)... Default: You are a helpful assistant. Please provide clear, accurate, and well-structured responses."
            rows={3}
            className="w-full px-3 py-2 text-light-primary dark:text-dark-primary bg-white dark:bg-dark-component-subtle border border-light-border dark:border-dark-border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-light-accent dark:focus:ring-dark-accent focus:border-light-accent dark:focus:border-dark-accent transition-colors duration-300"
            disabled={isLoading}
          />
        )}
      </div>
      
      <div>
        <h4 className="text-xs uppercase tracking-wide text-light-secondary dark:text-dark-secondary mb-2 font-medium transition-colors duration-300">User Prompt</h4>
        <textarea
          value={prompt}
          onChange={(e) => setPrompt(e.target.value)}
          placeholder="Enter your prompt here..."
          rows={6}
          className="w-full px-3 py-2 text-light-primary dark:text-dark-primary bg-white dark:bg-dark-component-subtle border border-light-border dark:border-dark-border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-light-accent dark:focus:ring-dark-accent focus:border-light-accent dark:focus:border-dark-accent transition-colors duration-300"
          disabled={isLoading}
        />
      </div>
      
      <div>
        <h4 className="text-xs uppercase tracking-wide text-light-secondary dark:text-dark-secondary mb-2 font-medium transition-colors duration-300">Select Generator Models</h4>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
          {availableModels.map((model) => {
            const isSelected = selectedModels.includes(model);
            const providerName = getProviderName(model);
            const modelName = getModelDisplayName(model);
            
            return (
              <label
                key={model}
                htmlFor={`model-gen-${model}`}
                className={`flex items-center p-2 rounded border transition-colors duration-200 cursor-pointer
                  ${isSelected
                    ? 'border-light-accent dark:border-dark-accent'
                    : 'border-light-border/30 dark:border-dark-border/30 hover:border-light-border dark:hover:border-dark-border'}
                  ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}
                `}
              >
                <input
                  id={`model-gen-${model}`}
                  type="checkbox"
                  value={model}
                  checked={isSelected}
                  onChange={handleModelChange}
                  disabled={isLoading}
                  className="mr-2 text-light-accent dark:text-dark-accent focus:ring-light-accent dark:focus:ring-dark-accent transition-colors duration-200"
                />
                <div className="flex items-center">
                  <Bot className="mr-1 text-light-secondary dark:text-dark-secondary" size={14} />
                  <div className="flex flex-col">
                    {providerName && (
                      <span className="text-xs text-light-secondary dark:text-dark-secondary">{providerName}</span>
                    )}
                    <span className="font-medium text-sm text-light-primary dark:text-dark-primary">{modelName}</span>
                  </div>
                </div>
              </label>
            );
          })}
        </div>
        {selectedModels.length === 0 && (
          <p className="mt-2 text-sm text-light-error dark:text-dark-error transition-colors duration-300">
            Please select at least one model
          </p>
        )}
      </div>
      
      <div className="flex justify-end">
        <button
          type="submit"
          disabled={isLoading || selectedModels.length === 0 || !prompt.trim()}
          className={`px-4 py-2 rounded-md text-white font-medium text-sm transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-offset-dark-component-subtle
            ${
              isLoading || selectedModels.length === 0 || !prompt.trim()
                ? 'bg-light-secondary/70 dark:bg-dark-secondary/70 cursor-not-allowed'
                : 'bg-light-accent dark:bg-dark-accent hover:bg-light-accent-hover dark:hover:bg-dark-accent-hover focus:ring-light-accent dark:focus:ring-dark-accent'
            }`}
        >
          {isLoading ? (
            <>
              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Generating...
            </>
          ) : (
            <>
              Generate Outputs
            </>
          )}
        </button>
      </div>
    </form>
  );
};

export default PromptInput; 