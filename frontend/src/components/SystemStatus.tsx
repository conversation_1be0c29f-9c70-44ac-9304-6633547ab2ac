import React, { useEffect } from 'react';
import useHealthStore from '../store/healthStore';
import { HealthStatus } from '../api/apiClient';

const SystemStatus: React.FC = () => {
  const { health, isLoading, startHealthMonitoring, stopHealthMonitoring } = useHealthStore();

  useEffect(() => {
    startHealthMonitoring();
    
    return () => {
      stopHealthMonitoring();
    };
  }, [startHealthMonitoring, stopHealthMonitoring]);

  const getStatusColor = (status?: HealthStatus) => {
    switch (status) {
      case HealthStatus.HEALTHY:
        return 'text-green-500';
      case HealthStatus.DEGRADED:
        return 'text-yellow-500';
      case HealthStatus.UNHEALTHY:
        return 'text-red-500';
      default:
        return 'text-gray-500';
    }
  };

  const getStatusText = (status?: HealthStatus) => {
    switch (status) {
      case HealthStatus.HEALTHY:
        return 'System Active';
      case HealthStatus.DEGRADED:
        return 'System Degraded';
      case HealthStatus.UNHEALTHY:
        return 'System Issues';
      default:
        return 'Checking...';
    }
  };

  const getStatusDotClass = (status?: HealthStatus) => {
    switch (status) {
      case HealthStatus.HEALTHY:
        return 'bg-green-500 animate-pulse';
      case HealthStatus.DEGRADED:
        return 'bg-yellow-500 animate-ping';
      case HealthStatus.UNHEALTHY:
        return 'bg-red-500 animate-bounce';
      default:
        return 'bg-gray-500 animate-pulse';
    }
  };

  const status = health?.status;

  if (isLoading && !health) {
    return (
      <div className="hidden sm:flex items-center gap-2 text-xs text-light-secondary dark:text-dark-secondary bg-light-component-subtle/50 dark:bg-dark-component-subtle/50 px-3 py-1.5 rounded-full">
        <div className="w-2 h-2 bg-gray-500 rounded-full animate-pulse"></div>
        <span>Checking System...</span>
      </div>
    );
  }

  return (
    <div 
      className="hidden sm:flex items-center gap-2 text-xs text-light-secondary dark:text-dark-secondary bg-light-component-subtle/50 dark:bg-dark-component-subtle/50 px-3 py-1.5 rounded-full cursor-help transition-colors duration-200 hover:bg-light-component-subtle dark:hover:bg-dark-component-subtle"
      title={health ? `Last checked: ${new Date(health.timestamp).toLocaleTimeString()}` : 'System status unknown'}
    >
      <div className={`w-2 h-2 rounded-full ${getStatusDotClass(status)}`}></div>
      <span className={getStatusColor(status)}>{getStatusText(status)}</span>
      {health && health.version !== 'unknown' && (
        <span className="ml-1 opacity-60">v{health.version}</span>
      )}
    </div>
  );
};

export default SystemStatus; 