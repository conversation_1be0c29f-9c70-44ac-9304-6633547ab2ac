import React, { useState, useEffect } from 'react';
import { Bot } from 'lucide-react';

interface ModelSelectorProps {
  availableModels: string[];
  onSelectionChange: (models: string[]) => void;
  disabled?: boolean;
  initialSelection?: string[];
  title?: string;
}

const ModelSelector: React.FC<ModelSelectorProps> = ({
  availableModels,
  onSelectionChange,
  disabled = false,
  initialSelection = [],
  title = "Select Generator Models"
}) => {
  const [selectedModels, setSelectedModels] = useState<string[]>(initialSelection);
  
  useEffect(() => {
    if (initialSelection && initialSelection.length > 0) {
      setSelectedModels(initialSelection);
    }
  }, [initialSelection]);
  
  const toggleModel = (model: string) => {
    const newSelectedModels = selectedModels.includes(model)
      ? selectedModels.filter(m => m !== model)
      : [...selectedModels, model];
    
    setSelectedModels(newSelectedModels);
    onSelectionChange(newSelectedModels);
  };
  
  const getProviderName = (modelId: string) => {
    const parts = modelId.split('/');
    return parts.length > 1 ? parts[0] : '';
  };
  
  const getModelDisplayName = (modelId: string) => {
    const parts = modelId.split('/');
    return parts.length > 1 ? parts[1] : modelId;
  };

  return (
    <div className="space-y-2">
      {title && <h4 className="text-xs uppercase tracking-wide text-light-secondary dark:text-dark-secondary mb-2 font-medium transition-colors duration-300">{title}</h4>}
      
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
        {availableModels.map((model) => {
          const isSelected = selectedModels.includes(model);
          const providerName = getProviderName(model);
          const modelName = getModelDisplayName(model);
          
          return (
            <label
              key={model}
              className={`flex items-center p-2 rounded border transition-colors duration-200 cursor-pointer w-full min-w-0
                ${isSelected
                  ? 'border-light-accent dark:border-dark-accent'
                  : 'border-light-border/30 dark:border-dark-border/30 hover:border-light-border dark:hover:border-dark-border'}
                ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
              `}
            >
              <input
                type="checkbox"
                checked={isSelected}
                onChange={() => toggleModel(model)}
                disabled={disabled}
                className="mr-2 text-light-accent dark:text-dark-accent focus:ring-light-accent dark:focus:ring-dark-accent transition-colors duration-200 flex-shrink-0"
              />
              <div className="flex items-center min-w-0 flex-1">
                <Bot className="mr-1 text-light-secondary dark:text-dark-secondary flex-shrink-0" size={14} />
                <div className="flex flex-col min-w-0 flex-1">
                  {providerName && (
                    <span className="text-xs text-light-secondary dark:text-dark-secondary truncate">{providerName}</span>
                  )}
                  <span className="font-medium text-light-primary dark:text-dark-primary truncate">{modelName}</span>
                </div>
              </div>
            </label>
          );
        })}
      </div>
      
      {selectedModels.length === 0 && (
        <p className="text-sm text-light-error dark:text-dark-error transition-colors duration-300">Please select at least one model</p>
      )}
    </div>
  );
};

export default ModelSelector; 