import React, { useState, useEffect } from 'react';
import { Trash2, History, Clock, Search, SquarePen, PanelLeftClose, AlertCircle } from 'lucide-react';
import { format } from 'date-fns';
import { TaskHistoryItem } from '../api/apiClient';
import LoadingSpinner from './LoadingSpinner';
import DeleteConfirmModal from './DeleteConfirmModal';

interface HistorySidebarProps {
  taskHistory: TaskHistoryItem[];
  isLoading: boolean; 
  error: string | null;
  onSelectTask: (taskId: number) => void;
  onDeleteTask?: (taskId: number) => void;
  currentTaskId: number | null;
  onNewConversation: () => void;
  onToggleDesktopCollapse?: () => void;
}

const HistorySidebar: React.FC<HistorySidebarProps> = ({
  taskHistory,
  isLoading,
  error,
  onSelectTask,
  onDeleteTask,
  currentTaskId,
  onNewConversation,
  onToggleDesktopCollapse
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredTaskHistory, setFilteredTaskHistory] = useState<TaskHistoryItem[]>(taskHistory);
  const [isSearchVisible, setIsSearchVisible] = useState(false);
  const [deleteModal, setDeleteModal] = useState<{ isOpen: boolean; taskId: number | null; taskPreview: string }>({
    isOpen: false,
    taskId: null,
    taskPreview: ''
  });

  useEffect(() => {
    if (searchTerm === '') {
      setFilteredTaskHistory(taskHistory);
    } else {
      setFilteredTaskHistory(
        taskHistory.filter(task => 
          task.prompt_snippet.toLowerCase().includes(searchTerm.toLowerCase())
        )
      );
    }
  }, [searchTerm, taskHistory]);

  const handleDeleteClick = (e: React.MouseEvent, taskId: number) => {
    if (!onDeleteTask) return;
    e.stopPropagation();
    
    // Find the task to get its preview text
    const task = taskHistory.find(t => t.id === taskId);
    const taskPreview = task?.prompt_snippet || '';
    
    setDeleteModal({
      isOpen: true,
      taskId,
      taskPreview
    });
  };

  const handleDeleteConfirm = () => {
    if (deleteModal.taskId && onDeleteTask) {
      onDeleteTask(deleteModal.taskId);
    }
  };

  const handleDeleteCancel = () => {
    setDeleteModal({
      isOpen: false,
      taskId: null,
      taskPreview: ''
    });
  };

  // Get status color based on task status
  const getStatusStyles = (status: string): string => {
    switch (status) {
      case 'COMPLETED':
        return 'bg-green-100 text-green-700 dark:bg-green-700/20 dark:text-green-300';
      case 'GENERATING':
        return 'bg-blue-100 text-blue-700 dark:bg-blue-700/20 dark:text-blue-300';
      case 'EVALUATING':
        return 'bg-yellow-100 text-yellow-700 dark:bg-yellow-700/20 dark:text-yellow-300';
      case 'EVALUATION_DONE':
        return 'bg-purple-100 text-purple-700 dark:bg-purple-700/20 dark:text-purple-300';
      case 'FAILED':
        return 'bg-red-100 text-red-700 dark:bg-red-700/20 dark:text-red-300';
      case 'PENDING':
        return 'bg-gray-100 text-gray-700 dark:bg-neutral-700/30 dark:text-neutral-300';
      default:
        return 'bg-gray-100 text-gray-700 dark:bg-neutral-700/30 dark:text-neutral-300';
    }
  };

  return (
    <div className="flex flex-col h-full bg-light-component dark:bg-dark-component border-r border-light-border dark:border-dark-border shadow-sm">
      <div className="h-14 px-3 border-b border-light-border dark:border-dark-border flex items-center justify-between shrink-0">
        <div className="flex items-center gap-2">
          {onToggleDesktopCollapse && (
            <button
              onClick={onToggleDesktopCollapse}
              className="p-2 rounded-lg text-light-secondary dark:text-dark-secondary hover:text-light-primary dark:hover:text-dark-primary hover:bg-light-component-subtle dark:hover:bg-dark-component-subtle transition-colors"
              title="Close sidebar"
            >
              <PanelLeftClose className="text-light-secondary dark:text-dark-secondary" size={16} />
            </button>
          )}
          
          <button
            onClick={() => setIsSearchVisible(!isSearchVisible)}
            className="p-2 rounded-lg text-light-secondary dark:text-dark-secondary hover:text-light-primary dark:hover:text-dark-primary hover:bg-light-component-subtle dark:hover:bg-dark-component-subtle transition-colors"
            title="Search history"
          >
            <Search className="text-light-secondary dark:text-dark-secondary" size={16} />
          </button>
        </div>
        
        <button
          onClick={onNewConversation}
          className="p-2 rounded-lg text-light-secondary dark:text-dark-secondary hover:text-light-primary dark:hover:text-dark-primary hover:bg-light-component-subtle dark:hover:bg-dark-component-subtle transition-colors"
          title="New Conversation"
        >
          <SquarePen className="text-light-secondary dark:text-dark-secondary" size={16} />
        </button>
      </div>
      
      <div className="pt-2 pb-1 px-3 shrink-0">
        {isSearchVisible && (
          <div className="relative mb-2">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search className="h-4 w-4 text-text-secondary" />
            </div>
            <input
              type="text"
              placeholder="Search history..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-3 py-2 text-sm bg-light-component-subtle dark:bg-dark-component-subtle border border-border-primary rounded-md focus:ring-light-accent dark:focus:ring-dark-accent focus:border-light-accent dark:focus:border-dark-accent placeholder-text-tertiary text-text-primary"
              autoFocus
            />
          </div>
        )}
        <div className="h-10 flex items-center justify-between">
          <h2 className="text-base font-semibold text-text-primary flex items-center">
            <History className="mr-2 text-light-accent dark:text-dark-accent" size={16} />
            History
          </h2>
        </div>
      </div>
      
      <div className="flex-1 overflow-y-auto py-2 px-1 custom-scrollbar">
        {isLoading ? (
          <div className="p-4 flex justify-center">
            <LoadingSpinner message="Loading history..." />
          </div>
        ) : error ? (
          <div className="p-3 text-light-error dark:text-dark-error text-xs rounded-md bg-light-error-bg dark:bg-dark-error-bg border border-light-error/30 dark:border-dark-error/30 mx-1">
            <div className="flex items-center"><AlertCircle className="mr-2" size={14} />{error}</div>
          </div>
        ) : filteredTaskHistory.length === 0 ? (
          <div className="p-4 text-center mt-4">
            <div className="text-text-secondary/50 mb-1">
              <Clock className="mx-auto text-xl mb-1" size={20} />
            </div>
            <p className="text-xs text-text-secondary">
              {searchTerm ? `No results for "${searchTerm}"` : 'No history yet'}
            </p>
          </div>
        ) : (
          <ul className="space-y-1">
            {filteredTaskHistory.map((task) => (
              <li key={task.id} className='px-1'>
                <div
                  onClick={() => onSelectTask(task.id)}
                  title={task.prompt_snippet}
                  className={`w-full text-left rounded-md flex items-center justify-between text-sm transition-colors duration-150 hover:bg-light-component-subtle dark:hover:bg-dark-component-subtle focus:outline-none cursor-pointer px-3 py-2
                    ${task.id === currentTaskId ? 'bg-light-accent/10 dark:bg-dark-accent/10 ring-1 ring-light-accent/50 dark:ring-dark-accent/50' : ''}`
                  }
                  role="button"
                  tabIndex={0}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                      onSelectTask(task.id);
                    }
                  }}
                >
                  <div className="flex-1 min-w-0 pr-2">
                    <span className="font-medium text-sm text-light-primary dark:text-dark-primary line-clamp-1">
                      {task.prompt_snippet}
                    </span>
                    <div className="flex items-center mt-1 text-xs">
                      <span className={`px-1.5 py-0.5 rounded-sm font-medium text-xs ${getStatusStyles(task.status)} mr-2`}>
                        {task.status}
                      </span>
                      <span className="text-light-secondary dark:text-dark-secondary truncate">
                        {format(new Date(task.created_at), 'MMM d, HH:mm')}
                      </span>
                    </div>
                  </div>
                  {onDeleteTask && (
                    <button 
                      onClick={(e) => handleDeleteClick(e, task.id)} 
                      className="p-1.5 rounded-md text-light-secondary/70 dark:text-dark-secondary/70 hover:text-light-error dark:hover:text-dark-error hover:bg-light-component-subtle dark:hover:bg-dark-component-subtle focus:outline-none transition-colors duration-150 shrink-0"
                      title="Delete Task"
                    >
                      <Trash2 size={14} />
                    </button>
                  )}
                </div>
              </li>
            ))}
          </ul>
        )}
      </div>
      
      {/* Delete Confirmation Modal */}
      <DeleteConfirmModal
        isOpen={deleteModal.isOpen}
        onClose={handleDeleteCancel}
        onConfirm={handleDeleteConfirm}
        taskId={deleteModal.taskId || 0}
        taskPreview={deleteModal.taskPreview}
      />
    </div>
  );
};

export default HistorySidebar; 