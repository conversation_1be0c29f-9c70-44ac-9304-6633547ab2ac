/* Import Tailwind directives */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Linear-inspired base styles */
:root {
  --linear-background-light: #F9FAFB;
  --linear-background-dark: #171717;
  --linear-card-light: #FFFFFF;
  --linear-card-dark: #1F1F1F;
  --linear-accent: #5E6AD2;
  --linear-accent-hover: #4954BD;
  --linear-border-light: #EAECEF;
  --linear-border-dark: #2D2D2D;
  --linear-text-primary-light: #11111B;
  --linear-text-primary-dark: #FFFFFF;
  --linear-text-secondary-light: #687076;
  --linear-text-secondary-dark: #A0A0A0;
}

/* Base styles */
body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  @apply bg-gray-50 dark:bg-neutral-900 text-gray-900 dark:text-white;
}

/* Apply smooth transitions for theme switching */
.transition-colors {
  transition-property: background-color, border-color, color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

/* Custom scrollbar styles */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.5);
  border-radius: 4px;
  border: 2px solid transparent;
  background-clip: content-box;
  transition: all 0.2s ease;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.8);
  background-clip: content-box;
}

.dark .custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(75, 85, 99, 0.6);
  background-clip: content-box;
}

.dark .custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(75, 85, 99, 0.9);
  background-clip: content-box;
}

/* Global scrollbar styles for main page scrollbar */
html {
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
}

html::-webkit-scrollbar {
  width: 12px;
}

html::-webkit-scrollbar-track {
  background: transparent;
}

html::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.5);
  border-radius: 6px;
  border: 2px solid transparent;
  background-clip: content-box;
  transition: all 0.2s ease;
}

html::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.8);
  background-clip: content-box;
}

/* Dark mode global scrollbar */
.dark html {
  scrollbar-color: rgba(156, 163, 175, 0.7) transparent;
}

.dark html::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.7);
  background-clip: content-box;
}

.dark html::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.9);
  background-clip: content-box;
}

/* Enhanced focus styles */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-light-accent dark:focus:ring-dark-accent focus:ring-offset-2 focus:ring-offset-light-component dark:focus:ring-offset-dark-component;
}

/* Button hover animations */
.btn-hover-lift {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-hover-lift:hover {
  transform: translateY(-1px);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1);
}

.dark .btn-hover-lift:hover {
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3), 0 8px 10px -6px rgba(0, 0, 0, 0.2);
}

/* Custom component styles */
@layer components {
  /* Linear-inspired buttons */
  .btn-primary {
    @apply px-4 py-2 bg-indigo-600 hover:bg-indigo-700 dark:bg-indigo-600 dark:hover:bg-indigo-500 
           text-white rounded-md shadow-sm transition duration-150 
           focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500
           disabled:opacity-50 disabled:cursor-not-allowed;
}

  .btn-secondary {
    @apply px-4 py-2 bg-white hover:bg-gray-100 dark:bg-neutral-800 dark:hover:bg-neutral-700 
           text-gray-700 dark:text-gray-200 border border-gray-300 dark:border-neutral-600 
           rounded-md shadow-sm transition duration-150
           focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 
           disabled:opacity-50 disabled:cursor-not-allowed;
  }

  /* Linear-inspired cards */
  .linear-card {
    @apply bg-white dark:bg-neutral-800 border border-gray-200 dark:border-neutral-700 
           rounded-md shadow-sm hover:shadow-md transition duration-150;
  }

  /* Linear-inspired sidebar */
  .linear-sidebar {
    @apply bg-gray-50 dark:bg-neutral-900 border-r border-gray-200 dark:border-neutral-700;
  }
  
  /* Linear-inspired form elements */
  .linear-input {
    @apply block w-full rounded-md border-gray-300 dark:border-neutral-600 shadow-sm 
           bg-white dark:bg-neutral-800 text-gray-900 dark:text-white placeholder-gray-400
           dark:placeholder-neutral-500 focus:border-indigo-500 focus:ring focus:ring-indigo-500 
           focus:ring-opacity-50 transition duration-150
           disabled:opacity-60 disabled:cursor-not-allowed;
  }

  .linear-checkbox {
    @apply h-4 w-4 rounded border-gray-300 dark:border-neutral-600 
           text-indigo-600 focus:ring-indigo-500 transition duration-150;
}

  /* Selection styles */
  .linear-selected {
    @apply bg-indigo-50 dark:bg-indigo-900/30 border-l-2 border-indigo-500;
  }
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .linear-sidebar {
    @apply fixed inset-y-0 left-0 z-40 w-64 transform -translate-x-full lg:translate-x-0 transition duration-300;
  }
  
  .linear-sidebar.open {
    @apply translate-x-0;
  }

  .sidebar-backdrop {
    @apply fixed inset-0 bg-black bg-opacity-50 z-30 transition-opacity duration-300;
  }
}

/* Custom utilities */
@layer utilities {
  .border-3 {
    border-width: 3px;
  }
  
  .animate-spin-slow {
    animation: spin 2s linear infinite;
  }
  
  .backdrop-blur-xs {
    backdrop-filter: blur(2px);
  }
  
  /* Modal animation utilities */
  .animate-in {
    animation-fill-mode: both;
  }
  
  .fade-in {
    animation-name: fadeIn;
  }
  
  .zoom-in-95 {
    animation-name: zoomIn95;
  }
  
  .duration-200 {
    animation-duration: 200ms;
  }
  
  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }
}

/* Keyframe animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes zoomIn95 {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
