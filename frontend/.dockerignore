# Dependencies
node_modules
npm-debug.log*

# Build outputs
dist
build
.next

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE files
.vscode
.idea
*.swp
*.swo

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git
.gitignore

# Testing
coverage
.nyc_output

# Cache directories
.cache
.parcel-cache

# Logs
logs
*.log

# Docker
Dockerfile*
.dockerignore

# Documentation
README.md
docs/

# CI/CD
.github 