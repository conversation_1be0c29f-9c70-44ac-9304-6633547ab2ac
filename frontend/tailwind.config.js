/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ["./src/**/*.{js,jsx,ts,tsx}"],
  darkMode: 'class', // Enable dark mode using class strategy
  theme: {
    extend: {
      fontFamily: {
        sans: ['Inter', 'system-ui', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', 'sans-serif'],
      },
      colors: {
        // Semantic names for colors
        // Light Theme Colors
        light: {
          primary: '#111827',      // Default text, headings (Almost Black)
          secondary: '#4B5563',  // Subdued text, icons (Gray-600)
          accent: '#4f46e5',      // Accent color (Indigo-600)
          'accent-hover': '#4338ca', // Accent hover (Indigo-700)
          background: '#F9FAFB', // Page background (Neutral-50)
          component: '#FFFFFF',   // Component background (White)
          'component-subtle': '#F3F4F6', // Subtle component background (Neutral-100)
          border: '#E5E7EB',     // Default border (Neutral-200)
          'border-strong': '#D1D5DB', // Stronger border (Neutral-300)
          error: '#EF4444',       // Error text (Red-500)
          'error-bg': '#FEF2F2', // Error background (Red-50)
        },
        // Dark Theme Colors
        dark: {
          primary: '#F3F4F6',      // Default text, headings (Neutral-100)
          secondary: '#9CA3AF',  // Subdued text, icons (Neutral-400)
          accent: '#6366F1',      // Accent color (Indigo-500)
          'accent-hover': '#4f46e5', // Accent hover (Indigo-600)
          background: '#111827', // Page background (Neutral-900)
          component: '#1F2937',   // Component background (Neutral-800)
          'component-subtle': '#374151', // Subtle component background (Neutral-700)
          border: '#374151',     // Default border (Neutral-700)
          'border-strong': '#4B5563', // Stronger border (Neutral-600)
          error: '#F87171',       // Error text (Red-400)
          'error-bg': '#450a0a', // Error background (Red-900/50 -> approximation)
        },
        // Original neutral palette (can still be used for specific cases if needed)
        neutral: {
          50: '#F9FAFB',
          100: '#F3F4F6',
          200: '#E5E7EB',
          300: '#D1D5DB',
          400: '#9CA3AF',
          500: '#6B7280',
          600: '#4B5563',
          700: '#374151',
          800: '#1F2937',
          900: '#111827',
        },
        // Specific indigo colors for direct use if needed
        indigo: {
          50: '#eef2ff',
          100: '#e0e7ff',
          200: '#c7d2fe',
          300: '#a5b4fc',
          400: '#818cf8',
          500: '#6366f1',
          600: '#4f46e5',
          700: '#4338ca',
          800: '#3730a3',
          900: '#312e81',
        },
      },
      // Typography plugin setup
      typography: (theme) => ({
        DEFAULT: {
          css: {
            '--tw-prose-body': theme('colors.light.primary'),
            '--tw-prose-headings': theme('colors.light.primary'),
            '--tw-prose-lead': theme('colors.light.secondary'),
            '--tw-prose-links': theme('colors.light.accent'),
            '--tw-prose-bold': theme('colors.light.primary'),
            '--tw-prose-counters': theme('colors.light.secondary'),
            '--tw-prose-bullets': theme('colors.light.border-strong'),
            '--tw-prose-hr': theme('colors.light.border'),
            '--tw-prose-quotes': theme('colors.light.primary'),
            '--tw-prose-quote-borders': theme('colors.light.border'),
            '--tw-prose-captions': theme('colors.light.secondary'),
            '--tw-prose-code': theme('colors.light.accent'),
            '--tw-prose-pre-code': theme('colors.neutral.200'), // Light theme code block text
            '--tw-prose-pre-bg': theme('colors.light.component'),   // Light theme code block background, matches page bg
            '--tw-prose-th-borders': theme('colors.light.border-strong'),
            '--tw-prose-td-borders': theme('colors.light.border'),
            'code::before': { content: 'none' },
            'code::after': { content: 'none' },
          },
        },
        dark: {
          css: {
            '--tw-prose-body': theme('colors.dark.primary'),
            '--tw-prose-headings': theme('colors.dark.primary'),
            '--tw-prose-lead': theme('colors.dark.secondary'),
            '--tw-prose-links': theme('colors.dark.accent'),
            '--tw-prose-bold': theme('colors.dark.primary'),
            '--tw-prose-counters': theme('colors.dark.secondary'),
            '--tw-prose-bullets': theme('colors.dark.border-strong'),
            '--tw-prose-hr': theme('colors.dark.border'),
            '--tw-prose-quotes': theme('colors.dark.primary'),
            '--tw-prose-quote-borders': theme('colors.dark.border'),
            '--tw-prose-captions': theme('colors.dark.secondary'),
            '--tw-prose-code': theme('colors.dark.accent'), // Inline code
            '--tw-prose-pre-code': theme('colors.neutral.200'),      // Dark theme code block text (often light on dark bg)
            '--tw-prose-pre-bg': theme('colors.neutral.800'), // Dark theme code block background (can be shared or specific)
            '--tw-prose-th-borders': theme('colors.dark.border-strong'),
            '--tw-prose-td-borders': theme('colors.dark.border'),
            'code::before': { content: 'none' },
            'code::after': { content: 'none' },
          },
        },
      }),
    },
  },
  plugins: [
    require('@tailwindcss/typography'),
  ],
}; 