import js from '@eslint/js'
import globals from 'globals'
import reactHooks from 'eslint-plugin-react-hooks'
import reactRefresh from 'eslint-plugin-react-refresh'
import tseslint from 'typescript-eslint'

// Helper to get __dirname in ESM if needed, or use process.cwd()
// For simplicity, assuming eslint.config.js might be CJS or ESM that allows __dirname, or using process.cwd()
// const currentDir = typeof __dirname !== 'undefined' ? __dirname : process.cwd(); 
// If your eslint.config.js is definitively ESM, import.meta.dirname is better if available & configured.
// For now, we'll assume tsconfigRootDir can be relative from where eslint is run, or use a placeholder.

export default [
  { ignores: ['dist/**', 'node_modules/**'] }, // Added node_modules to ignores
  
  // Configuration for eslint.config.js itself (ESM)
  {
    files: ['eslint.config.js'],
    languageOptions: {
      sourceType: 'module',
      globals: { ...globals.node }, // For `process.cwd()` or other Node.js globals if used directly
    },
    rules: {
        ...js.configs.recommended.rules,
    }
  },

  // Configuration for other specific JS config files
  {
    files: ['tailwind.config.js'], // Assuming CommonJS
    languageOptions: {
      sourceType: 'commonjs',
      globals: { ...globals.node },
    },
    rules: {
        ...js.configs.recommended.rules,
        // Disable or adjust rules that might conflict with CJS module system if necessary
        // For example, if it complains about `module.exports` not being an ESM export.
    }
  },
  {
    files: ['postcss.config.js'], // Assuming ESM as it had import/export error before
    languageOptions: {
        sourceType: 'module',
        globals: { ...globals.node }, // Or browser if it's for client-side postcss
    },
    rules: {
        ...js.configs.recommended.rules,
    }
  },
  
  // TypeScript specific configurations
  {
    files: ['**/*.{ts,tsx}'],
    languageOptions: {
      parser: tseslint.parser,
      parserOptions: {
        project: ['./tsconfig.app.json', './tsconfig.node.json'],
        tsconfigRootDir: process.cwd(),
      },
      globals: {
        ...globals.browser,
      },
    },
    plugins: {
      '@typescript-eslint': tseslint.plugin,
      'react-hooks': reactHooks,
      'react-refresh': reactRefresh,
    },
    rules: {
      ...tseslint.configs.recommended.rules,
      ...reactHooks.configs.recommended.rules,
      'react-refresh/only-export-components': [
        'warn',
        { allowConstantExport: true },
      ],
      '@typescript-eslint/no-unused-vars': [
        'warn',
        {
          args: 'all',
          argsIgnorePattern: '^_',
          varsIgnorePattern: '^_',
          caughtErrorsIgnorePattern: '^_',
          ignoreRestSiblings: true,
        },
      ],
      '@typescript-eslint/no-explicit-any': [
        'warn',
        {
          fixToUnknown: true,
          ignoreRestArgs: true,
        },
      ],
    },
  },
  // Specific config for vite.config.ts to be treated as ESM
  {
    files: ['vite.config.ts'],
    languageOptions: {
        sourceType: 'module',
        globals: { ...globals.node } // vite.config.ts runs in Node
    }
    // No specific rules needed here unless overriding, will inherit from TS block if it matches files pattern
  }
]
