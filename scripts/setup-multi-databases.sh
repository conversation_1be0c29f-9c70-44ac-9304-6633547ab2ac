#!/bin/bash

# Setup multiple database instances with initial schema and downgrade to baseline version
# For AI tool testing environment

set -e

# Define AI tools list
AI_TOOLS=("augment" "roo" "cline" "cursor" "copilot" "windsurf")

# Baseline version (from documentation)
BASELINE_VERSION="28b5937d928a"

# Namespace
NAMESPACE="llm-eval"

echo "🚀 Starting multi-database instance setup..."
echo "📊 AI Tools: ${AI_TOOLS[*]}"
echo "📍 Target baseline version: $BASELINE_VERSION"
echo ""

for tool in "${AI_TOOLS[@]}"; do
    echo "🔧 Processing $tool database..."
    
    # Build database connection URL - Use sync format for Alembic
    # Override the app's async URL with sync format for migrations
    DB_URL="postgresql://postgres:postgres123@postgresql-${tool}-service:5432/llm_eval_${tool}"
    
    echo "  📡 Testing connection: postgresql-${tool}-service"
    
    # Test database connection
    kubectl run psql-test-${tool} --rm -it --image=postgres:15-alpine --restart=Never \
        --namespace=$NAMESPACE \
        -- psql "$DB_URL" -c "SELECT 1;" >/dev/null 2>&1 || {
        echo "  ❌ $tool database connection failed"
        exit 1
    }
    
    echo "  ✅ Connection successful"
    
    # Run full migration to latest version (establish complete schema first)
    # Override DATABASE_URL to use sync format that works with psycopg2
    echo "  📤 Running migration to latest version..."
    kubectl exec -n $NAMESPACE deployment/backend -- \
        bash -c "cd /app && POSTGRES_DB=temp DATABASE_URL='$DB_URL' alembic upgrade head"
    
    # Check current version
    CURRENT_VERSION=$(kubectl exec -n $NAMESPACE deployment/backend -- \
        bash -c "cd /app && POSTGRES_DB=temp DATABASE_URL='$DB_URL' alembic current" | grep -E '^[a-f0-9]+' | head -n1)
    
    echo "  📋 Current version: $CURRENT_VERSION"
    
    # Downgrade to baseline version
    echo "  📉 Downgrading to baseline version: $BASELINE_VERSION"
    kubectl exec -n $NAMESPACE deployment/backend -- \
        bash -c "cd /app && POSTGRES_DB=temp DATABASE_URL='$DB_URL' alembic downgrade $BASELINE_VERSION"
    
    # Verify downgrade version
    FINAL_VERSION=$(kubectl exec -n $NAMESPACE deployment/backend -- \
        bash -c "cd /app && POSTGRES_DB=temp DATABASE_URL='$DB_URL' alembic current" | grep -E '^[a-f0-9]+' | head -n1)
    
    echo "  ✅ $tool database setup completed (version: $FINAL_VERSION)"
    echo ""
done

echo "🎉 All database instances setup completed!"
echo ""
echo "📝 Database connection information:"
for tool in "${AI_TOOLS[@]}"; do
    echo "  $tool: postgresql://postgres:postgres123@postgresql-${tool}-service:5432/llm_eval_${tool}"
done 