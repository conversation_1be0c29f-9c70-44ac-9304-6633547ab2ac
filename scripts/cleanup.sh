#!/bin/bash

# Cleanup script for LLM Evaluation Platform k3s deployment
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
NAMESPACE="llm-eval"

echo -e "${YELLOW}🧹 Cleaning up LLM Evaluation Platform deployment...${NC}"

# Check if kubectl is available
if ! command -v kubectl >/dev/null 2>&1; then
    echo -e "${RED}❌ kubectl is not installed${NC}"
    exit 1
fi

# Check if namespace exists
if kubectl get namespace $NAMESPACE >/dev/null 2>&1; then
    echo -e "${YELLOW}📋 Found namespace: $NAMESPACE${NC}"
    
    # Show current resources
    echo -e "${YELLOW}📊 Current resources in namespace:${NC}"
    kubectl get all -n $NAMESPACE
    
    # Ask for confirmation
    echo -e "${YELLOW}⚠️  This will delete all resources in the '$NAMESPACE' namespace.${NC}"
    read -p "Are you sure you want to continue? (y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo -e "${YELLOW}🗑️  Deleting namespace and all resources...${NC}"
        kubectl delete namespace $NAMESPACE
        
        echo -e "${GREEN}✅ Cleanup completed successfully!${NC}"
        echo -e "${GREEN}🎉 All resources have been removed from k3s${NC}"
    else
        echo -e "${YELLOW}❌ Cleanup cancelled${NC}"
        exit 0
    fi
else
    echo -e "${YELLOW}ℹ️  Namespace '$NAMESPACE' does not exist or has already been deleted${NC}"
fi

# Optional: Clean up Docker images
echo -e "${YELLOW}🐳 Docker image cleanup${NC}"
read -p "Do you want to remove local Docker images? (y/N): " -n 1 -r
echo

if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${YELLOW}🗑️  Removing Docker images...${NC}"
    
    # Remove backend image
    if docker images | grep -q "llm-eval-backend"; then
        docker rmi llm-eval-backend:latest || echo "Failed to remove backend image"
    fi
    
    # Remove frontend image
    if docker images | grep -q "llm-eval-frontend"; then
        docker rmi llm-eval-frontend:latest || echo "Failed to remove frontend image"
    fi
    
    echo -e "${GREEN}✅ Docker images cleaned up${NC}"
else
    echo -e "${YELLOW}ℹ️  Docker images kept${NC}"
fi

echo -e "${GREEN}🎉 Cleanup process completed!${NC}" 