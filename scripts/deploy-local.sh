#!/bin/bash

# Local k3s deployment script for LLM Evaluation Platform
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
NAMESPACE="llm-eval"
BACKEND_IMAGE="llm-eval-backend:latest"
FRONTEND_IMAGE="llm-eval-frontend:latest"

echo -e "${GREEN}🚀 Starting local k3s deployment for LLM Evaluation Platform${NC}"

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
echo -e "${YELLOW}📋 Checking prerequisites...${NC}"

if ! command_exists kubectl; then
    echo -e "${RED}❌ kubectl is not installed${NC}"
    exit 1
fi

if ! command_exists docker; then
    echo -e "${RED}❌ Docker is not installed${NC}"
    exit 1
fi

# Check if k3s is running
if ! kubectl cluster-info >/dev/null 2>&1; then
    echo -e "${RED}❌ k3s cluster is not accessible. Make sure k3s is running.${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Prerequisites check passed${NC}"

# Build Docker images
echo -e "${YELLOW}🔨 Building Docker images...${NC}"

echo "Building backend image..."
docker build -t $BACKEND_IMAGE ./backend

echo "Building frontend image..."
docker build -t $FRONTEND_IMAGE ./frontend

# Import images to k3s (for local development)
echo -e "${YELLOW}📦 Importing images to k3s...${NC}"
docker save $BACKEND_IMAGE | sudo k3s ctr images import -
docker save $FRONTEND_IMAGE | sudo k3s ctr images import -

echo -e "${GREEN}✅ Images built and imported${NC}"

# Deploy to k3s
echo -e "${YELLOW}🚀 Deploying to k3s...${NC}"

# Create namespace
echo "Creating namespace..."
kubectl apply -f k8s/namespace.yaml

# Deploy PostgreSQL
echo "Deploying PostgreSQL..."
kubectl apply -f k8s/postgresql.yaml

# Wait for PostgreSQL to be ready
echo "Waiting for PostgreSQL to be ready..."
kubectl wait --for=condition=ready pod -l app=postgresql -n $NAMESPACE --timeout=300s

# Deploy backend
echo "Deploying backend..."
kubectl apply -f k8s/backend.yaml

# Wait for backend to be ready
echo "Waiting for backend to be ready..."
kubectl wait --for=condition=ready pod -l app=backend -n $NAMESPACE --timeout=300s

# Deploy frontend
echo "Deploying frontend..."
kubectl apply -f k8s/frontend.yaml

# Wait for frontend to be ready
echo "Waiting for frontend to be ready..."
kubectl wait --for=condition=ready pod -l app=frontend -n $NAMESPACE --timeout=300s

echo -e "${GREEN}✅ Deployment completed successfully!${NC}"

# Show deployment status
echo -e "${YELLOW}📊 Deployment Status:${NC}"
kubectl get pods -n $NAMESPACE
echo ""
kubectl get services -n $NAMESPACE

# Get frontend URL
FRONTEND_PORT=$(kubectl get service frontend-service -n $NAMESPACE -o jsonpath='{.spec.ports[0].nodePort}' 2>/dev/null || echo "")
if [ -n "$FRONTEND_PORT" ]; then
    echo -e "${GREEN}🌐 Frontend is accessible at: http://localhost:$FRONTEND_PORT${NC}"
else
    # For LoadBalancer service, get external IP
    EXTERNAL_IP=$(kubectl get service frontend-service -n $NAMESPACE -o jsonpath='{.status.loadBalancer.ingress[0].ip}' 2>/dev/null || echo "")
    if [ -n "$EXTERNAL_IP" ]; then
        echo -e "${GREEN}🌐 Frontend is accessible at: http://$EXTERNAL_IP:3000${NC}"
    else
        echo -e "${YELLOW}🌐 Frontend service is starting up. Check with: kubectl get services -n $NAMESPACE${NC}"
    fi
fi

# Show useful commands
echo -e "${YELLOW}📝 Useful commands:${NC}"
echo "  View pods: kubectl get pods -n $NAMESPACE"
echo "  View services: kubectl get services -n $NAMESPACE"
echo "  View logs: kubectl logs -l app=backend -n $NAMESPACE"
echo "  Port forward frontend: kubectl port-forward service/frontend-service 3000:3000 -n $NAMESPACE"
echo "  Port forward backend: kubectl port-forward service/backend-service 8000:8000 -n $NAMESPACE"
echo "  Delete deployment: kubectl delete namespace $NAMESPACE"

echo -e "${GREEN}🎉 Deployment script completed!${NC}" 