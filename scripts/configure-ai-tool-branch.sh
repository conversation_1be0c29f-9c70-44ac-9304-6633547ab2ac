#!/bin/bash

# Configure AI Tool Testing Branch
# Usage: ./configure-ai-tool-branch.sh <tool-name>
# Example: ./configure-ai-tool-branch.sh augment

set -e

TOOL_NAME="${1}"
VALID_TOOLS=("augment" "roo" "cline" "cursor" "copilot" "windsurf")

function show_usage() {
    echo "Usage: $0 <tool-name>"
    echo ""
    echo "Configure branch for AI tool testing with dedicated database connection"
    echo ""
    echo "Valid tool names:"
    for tool in "${VALID_TOOLS[@]}"; do
        echo "  - $tool"
    done
    echo ""
    echo "Example: $0 augment"
}

function validate_tool() {
    if [[ -z "$TOOL_NAME" ]]; then
        echo "❌ Error: Tool name is required"
        show_usage
        exit 1
    fi
    
    if [[ ! " ${VALID_TOOLS[@]} " =~ " ${TOOL_NAME} " ]]; then
        echo "❌ Error: Invalid tool name '$TOOL_NAME'"
        show_usage
        exit 1
    fi
}

function update_backend_config() {
    echo "📝 Updating backend configuration for $TOOL_NAME..."
    
    # Update k8s/backend.yaml to use tool-specific database
    DB_SERVICE="postgresql-${TOOL_NAME}-service"
    DB_NAME="llm_eval_${TOOL_NAME}"
    
    # Create tool-specific backend yaml
    cp k8s/backend.yaml "k8s/backend-${TOOL_NAME}.yaml"
    
    # Update database connection in the tool-specific config
    sed -i "s/postgresql-service/${DB_SERVICE}/g" "k8s/backend-${TOOL_NAME}.yaml"
    sed -i "s/llm_eval/${DB_NAME}/g" "k8s/backend-${TOOL_NAME}.yaml"
    
    # Update deployment name to avoid conflicts
    sed -i "s/name: backend/name: backend-${TOOL_NAME}/g" "k8s/backend-${TOOL_NAME}.yaml"
    sed -i "s/app: backend/app: backend-${TOOL_NAME}/g" "k8s/backend-${TOOL_NAME}.yaml"
    
    echo "✅ Created k8s/backend-${TOOL_NAME}.yaml"
}

function update_ci_workflow() {
    echo "📝 Creating CI/CD workflow for $TOOL_NAME..."
    
    # Create tool-specific workflow file
    cp .github/workflows/ci-cd.yaml ".github/workflows/ci-cd-${TOOL_NAME}.yaml"
    
    # Update workflow name and trigger branches
    sed -i "s/name: CI\/CD Pipeline/name: CI\/CD Pipeline - ${TOOL_NAME^^}/g" ".github/workflows/ci-cd-${TOOL_NAME}.yaml"
    sed -i "s/- cursor/- ai-test\/${TOOL_NAME}/g" ".github/workflows/ci-cd-${TOOL_NAME}.yaml"
    
    # Update backend deployment to use tool-specific config
    sed -i "s/k8s\/backend.yaml/k8s\/backend-${TOOL_NAME}.yaml/g" ".github/workflows/ci-cd-${TOOL_NAME}.yaml"
    
    echo "✅ Created .github/workflows/ci-cd-${TOOL_NAME}.yaml"
}

function create_branch_readme() {
    echo "📝 Creating branch-specific README..."
    
    cat > "README-${TOOL_NAME}.md" << EOF
# AI Tool Testing Branch: ${TOOL_NAME^^}

This branch is configured for testing **${TOOL_NAME}** AI tool capabilities.

## 🎯 Testing Configuration

- **Database**: \`llm_eval_${TOOL_NAME}\` on \`postgresql-${TOOL_NAME}-service:5432\`
- **Backend Config**: \`k8s/backend-${TOOL_NAME}.yaml\`
- **CI/CD Workflow**: \`.github/workflows/ci-cd-${TOOL_NAME}.yaml\`
- **Branch Trigger**: \`ai-test/${TOOL_NAME}\`

## 🚀 Quick Start

1. **Verify database is running**:
   \`\`\`bash
   kubectl get pods -n llm-eval | grep postgresql-${TOOL_NAME}
   \`\`\`

2. **Reset database to baseline**:
   \`\`\`bash
   ./scripts/setup-multi-databases.sh reset ${TOOL_NAME}
   \`\`\`

3. **Deploy application**:
   \`\`\`bash
   kubectl apply -f k8s/backend-${TOOL_NAME}.yaml
   \`\`\`

4. **Test connection**:
   \`\`\`bash
   kubectl exec -n llm-eval deployment/backend-${TOOL_NAME} -- curl -s http://localhost:8000/api/v1/health
   \`\`\`

## 📋 Testing Task: Usage Statistics Implementation

Execute the Usage Statistics functionality implementation challenge:

**Objective**: Add complete usage tracking for OpenRouter API calls in both generations and evaluations.

**Success Criteria**:
- [ ] Backend models updated with usage fields
- [ ] OpenRouter API integration with usage tracking
- [ ] Frontend display of usage statistics
- [ ] Single database migration script
- [ ] All tests passing

**Documentation**: See \`docs/ai-tool-test-task-usage-statistics.md\`

## 📊 Results Recording

Document your results in \`results/${TOOL_NAME}-usage-stats-results.md\`:
- Time taken
- Number of AI interactions needed  
- Code quality assessment
- Issues encountered and solutions
- Final working status

## 🔄 Reset Environment

To reset for a fresh test:
\`\`\`bash
# Reset database
./scripts/setup-multi-databases.sh reset ${TOOL_NAME}

# Redeploy application  
kubectl delete -f k8s/backend-${TOOL_NAME}.yaml
kubectl apply -f k8s/backend-${TOOL_NAME}.yaml
\`\`\`
EOF

    echo "✅ Created README-${TOOL_NAME}.md"
}

function create_results_template() {
    echo "📝 Creating results template..."
    
    mkdir -p results
    
    cat > "results/${TOOL_NAME}-usage-stats-results.md" << EOF
# ${TOOL_NAME^^} AI Tool Test Results - Usage Statistics Implementation

**Date**: $(date +%Y-%m-%d)  
**Tester**: [Your Name]  
**Task**: Usage Statistics Feature Implementation  
**Duration**: [Start Time] - [End Time] = [Total Duration]

## 🎯 Test Overview

**AI Tool**: ${TOOL_NAME}  
**Database**: llm_eval_${TOOL_NAME}  
**Starting Schema Version**: 28b5937d928a  
**Target**: Complete usage statistics implementation

## 📊 Results Summary

- **Status**: [ ] Success / [ ] Partial / [ ] Failed
- **AI Interactions**: [Number of prompts/conversations needed]
- **Human Interventions**: [Number of manual fixes required]
- **Code Quality**: [ ] Excellent / [ ] Good / [ ] Fair / [ ] Poor
- **Migration Success**: [ ] Single migration / [ ] Multiple migrations / [ ] Failed

## 📝 Detailed Log

### Phase 1: Initial Understanding
- **Time**: [HH:MM] - [HH:MM]
- **AI Prompt**: [First prompt given to AI]
- **AI Response**: [Summary of AI's initial understanding]
- **Issues**: [Any misunderstandings or gaps]

### Phase 2: Backend Implementation  
- **Time**: [HH:MM] - [HH:MM]
- **Generated Code**: [List of files modified/created]
- **Issues**: [Compilation errors, logic issues, etc.]
- **Fixes Required**: [Manual interventions needed]

### Phase 3: Database Migration
- **Time**: [HH:MM] - [HH:MM]
- **Migration Quality**: [Single/multiple migrations, correctness]
- **Issues**: [Migration failures, type issues, etc.]

### Phase 4: Frontend Implementation
- **Time**: [HH:MM] - [HH:MM]
- **Generated Code**: [Components created/modified]
- **UI Quality**: [Functionality and design assessment]

### Phase 5: Integration Testing
- **Time**: [HH:MM] - [HH:MM]
- **Test Results**: [API tests, E2E tests, manual testing]
- **Final Status**: [Working/broken features]

## 🐛 Issues Encountered

1. **Issue**: [Description]
   - **Cause**: [Root cause analysis]
   - **Solution**: [How it was resolved]
   - **AI vs Human**: [Who fixed it]

## 💡 AI Tool Assessment

### Strengths
- [What the AI tool did well]

### Weaknesses  
- [Areas where the AI tool struggled]

### Code Quality
- **Structure**: [Organization, modularity]
- **Best Practices**: [Following conventions, error handling]
- **Completeness**: [Missing functionality, edge cases]

## 🏆 Final Evaluation

**Overall Score**: [1-10]

**Recommendation**: [Would you recommend this AI tool for similar tasks?]

**Comparison Notes**: [How it compares to baseline implementation]
EOF

    echo "✅ Created results/${TOOL_NAME}-usage-stats-results.md"
}

function main() {
    echo "🔧 Configuring AI tool testing branch for: $TOOL_NAME"
    echo ""
    
    validate_tool
    update_backend_config
    update_ci_workflow  
    create_branch_readme
    create_results_template
    
    echo ""
    echo "✅ Branch configuration completed for $TOOL_NAME!"
    echo ""
    echo "Next steps:"
    echo "1. git add ."
    echo "2. git commit -m \"configure branch for $TOOL_NAME AI tool testing\""
    echo "3. git push origin ai-test/$TOOL_NAME"
    echo "4. Reset database: ./scripts/setup-multi-databases.sh reset $TOOL_NAME"
    echo "5. Start testing with your AI tool!"
}

main 