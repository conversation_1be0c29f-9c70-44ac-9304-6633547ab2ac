#!/bin/bash

# Start port forwarding for new cursor variant database instances
# Run in background to allow access from localhost

set -e

# Define new AI tools that need port forwarding
NEW_AI_TOOLS=("cursor-max" "cursor-gemini" "cursor-gemini-max")

# Namespace
NAMESPACE="llm-eval"

echo "🔗 Starting port forwarding for new database instances..."

# Kill any existing port forwards for these services
echo "🧹 Cleaning up existing port forwards..."
pkill -f 'kubectl port-forward.*postgresql-.*-service' 2>/dev/null || true

# Wait a moment for cleanup
sleep 2

for tool in "${NEW_AI_TOOLS[@]}"; do
    case $tool in
        "cursor-max")
            PORT=5439
            ;;
        "cursor-gemini")
            PORT=5440
            ;;
        "cursor-gemini-max")
            PORT=5441
            ;;
    esac
    
    echo "  🔌 Starting port forward for $tool: localhost:$PORT -> postgresql-${tool}-service:5432"
    kubectl port-forward -n $NAMESPACE service/postgresql-${tool}-service $PORT:5432 &
    
    # Store PID for cleanup
    echo $! > /tmp/port-forward-${tool}.pid
done

echo "  ⏳ Waiting for port forwards to establish..."
sleep 5

echo ""
echo "🎉 Port forwarding started successfully!"
echo ""
echo "📝 Database connection information:"
for tool in "${NEW_AI_TOOLS[@]}"; do
    case $tool in
        "cursor-max")
            PORT=5439
            ;;
        "cursor-gemini")
            PORT=5440
            ;;
        "cursor-gemini-max")
            PORT=5441
            ;;
    esac
    echo "  $tool: localhost:$PORT"
done

echo ""
echo "🔗 Port forwarding is running in background"
echo "📋 To stop port forwarding, run: pkill -f 'kubectl port-forward.*postgresql-.*-service'"
echo "📋 PID files stored in: /tmp/port-forward-*.pid"
echo ""
echo "💡 To test connection:"
echo "   psql postgresql://postgres:postgres123@localhost:5439/llm_eval_cursor_max"
echo "   psql postgresql://postgres:postgres123@localhost:5440/llm_eval_cursor_gemini"
echo "   psql postgresql://postgres:postgres123@localhost:5441/llm_eval_cursor_gemini_max" 