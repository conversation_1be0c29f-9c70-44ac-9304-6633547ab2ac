#!/bin/bash

# Local Docker Registry Management Script
# Usage: ./manage-local-registry.sh [start|stop|status|restart|clean|list]

REGISTRY_NAME="registry"
REGISTRY_PORT="5000"
REGISTRY_VOLUME="registry-data"

function start_registry() {
    echo "Starting local Docker registry..."
    
    # Check if registry is already running
    if docker ps --format "table {{.Names}}" | grep -q "^${REGISTRY_NAME}$"; then
        echo "Registry is already running"
        return 0
    fi
    
    # Check if registry container exists but is stopped
    if docker ps -a --format "table {{.Names}}" | grep -q "^${REGISTRY_NAME}$"; then
        echo "Starting existing registry container..."
        docker start ${REGISTRY_NAME}
    else
        echo "Creating new registry container..."
        docker run -d \
            -p ${REGISTRY_PORT}:5000 \
            --name ${REGISTRY_NAME} \
            --restart=always \
            -v ${REGISTRY_VOLUME}:/var/lib/registry \
            registry:2
    fi
    
    # Wait for registry to be ready
    echo "Waiting for registry to be ready..."
    sleep 3
    
    if curl -f http://localhost:${REGISTRY_PORT}/v2/ >/dev/null 2>&1; then
        echo "✅ Registry is running at localhost:${REGISTRY_PORT}"
    else
        echo "❌ Failed to start registry"
        return 1
    fi
}

function stop_registry() {
    echo "Stopping local Docker registry..."
    docker stop ${REGISTRY_NAME} 2>/dev/null || echo "Registry was not running"
}

function restart_registry() {
    echo "Restarting local Docker registry..."
    stop_registry
    sleep 2
    start_registry
}

function registry_status() {
    echo "=== Registry Status ==="
    
    if docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep -q "^${REGISTRY_NAME}"; then
        echo "✅ Registry is running"
        docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep "^${REGISTRY_NAME}"
        echo ""
        
        # Test connectivity
        if curl -f http://localhost:${REGISTRY_PORT}/v2/ >/dev/null 2>&1; then
            echo "✅ Registry API is accessible"
        else
            echo "❌ Registry API is not accessible"
        fi
    else
        echo "❌ Registry is not running"
    fi
    
    # Show volume status
    echo ""
    echo "=== Volume Status ==="
    docker volume ls | grep ${REGISTRY_VOLUME} || echo "Registry volume not found"
}

function list_images() {
    echo "=== Registry Contents ==="
    
    if ! curl -f http://localhost:${REGISTRY_PORT}/v2/ >/dev/null 2>&1; then
        echo "❌ Registry is not accessible"
        return 1
    fi
    
    echo "Repositories in registry:"
    curl -s http://localhost:${REGISTRY_PORT}/v2/_catalog | jq -r '.repositories[]?' 2>/dev/null || echo "No repositories found"
    
    echo ""
    echo "Detailed image list:"
    repositories=$(curl -s http://localhost:${REGISTRY_PORT}/v2/_catalog | jq -r '.repositories[]?' 2>/dev/null)
    
    if [ -n "$repositories" ]; then
        for repo in $repositories; do
            echo "Repository: $repo"
            tags=$(curl -s http://localhost:${REGISTRY_PORT}/v2/$repo/tags/list | jq -r '.tags[]?' 2>/dev/null)
            if [ -n "$tags" ]; then
                for tag in $tags; do
                    echo "  - localhost:${REGISTRY_PORT}/$repo:$tag"
                done
            else
                echo "  (no tags)"
            fi
            echo ""
        done
    else
        echo "No images found in registry"
    fi
}

function clean_registry() {
    echo "=== Cleaning Registry ==="
    echo "This will remove the registry container and all stored images"
    read -p "Are you sure? (y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo "Stopping and removing registry container..."
        docker stop ${REGISTRY_NAME} 2>/dev/null || true
        docker rm ${REGISTRY_NAME} 2>/dev/null || true
        
        echo "Removing registry volume..."
        docker volume rm ${REGISTRY_VOLUME} 2>/dev/null || true
        
        echo "✅ Registry cleaned"
    else
        echo "Operation cancelled"
    fi
}

function show_help() {
    echo "Local Docker Registry Management Script"
    echo ""
    echo "Usage: $0 [command]"
    echo ""
    echo "Commands:"
    echo "  start     Start the local registry"
    echo "  stop      Stop the local registry"
    echo "  restart   Restart the local registry"
    echo "  status    Show registry status"
    echo "  list      List images in registry"
    echo "  clean     Remove registry and all data"
    echo "  help      Show this help message"
    echo ""
    echo "Registry will be available at: localhost:${REGISTRY_PORT}"
}

# Parse command line arguments
case "${1:-help}" in
    start)
        start_registry
        ;;
    stop)
        stop_registry
        ;;
    restart)
        restart_registry
        ;;
    status)
        registry_status
        ;;
    list)
        list_images
        ;;
    clean)
        clean_registry
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        echo "Unknown command: $1"
        echo ""
        show_help
        exit 1
        ;;
esac 