#!/bin/bash

# Test script for local Docker registry
# This script tests the complete workflow: build -> push -> pull

set -e

REGISTRY="localhost:5000"
TEST_IMAGE="llm-eval/test"
TEST_TAG="latest"
FULL_IMAGE_NAME="${REGISTRY}/${TEST_IMAGE}:${TEST_TAG}"

echo "🧪 Testing Local Docker Registry Workflow"
echo "Registry: ${REGISTRY}"
echo "Test Image: ${FULL_IMAGE_NAME}"
echo ""

# Step 1: Check if registry is running
echo "1️⃣ Checking registry availability..."
if curl -f http://${REGISTRY}/v2/ >/dev/null 2>&1; then
    echo "✅ Registry is accessible"
else
    echo "❌ Registry is not accessible. Please start it first:"
    echo "   ./scripts/manage-local-registry.sh start"
    exit 1
fi
echo ""

# Step 2: Create a simple test Dockerfile
echo "2️⃣ Creating test Dockerfile..."
cat > /tmp/Dockerfile.test << EOF
FROM alpine:latest
RUN echo "Hello from local registry test!" > /hello.txt
CMD ["cat", "/hello.txt"]
EOF
echo "✅ Test Dockerfile created"
echo ""

# Step 3: Build test image
echo "3️⃣ Building test image..."
docker build -f /tmp/Dockerfile.test -t ${FULL_IMAGE_NAME} /tmp/
echo "✅ Image built successfully"
echo ""

# Step 4: Push to local registry
echo "4️⃣ Pushing image to local registry..."
docker push ${FULL_IMAGE_NAME}
echo "✅ Image pushed successfully"
echo ""

# Step 5: Remove local image to test pull
echo "5️⃣ Removing local image..."
docker rmi ${FULL_IMAGE_NAME}
echo "✅ Local image removed"
echo ""

# Step 6: Pull from local registry
echo "6️⃣ Pulling image from local registry..."
docker pull ${FULL_IMAGE_NAME}
echo "✅ Image pulled successfully"
echo ""

# Step 7: Test run the image
echo "7️⃣ Testing the pulled image..."
CONTAINER_OUTPUT=$(docker run --rm ${FULL_IMAGE_NAME})
if [ "$CONTAINER_OUTPUT" = "Hello from local registry test!" ]; then
    echo "✅ Image runs correctly: $CONTAINER_OUTPUT"
else
    echo "❌ Image output unexpected: $CONTAINER_OUTPUT"
    exit 1
fi
echo ""

# Step 8: Check registry contents
echo "8️⃣ Checking registry contents..."
echo "Repositories in registry:"
curl -s http://${REGISTRY}/v2/_catalog | jq -r '.repositories[]?' 2>/dev/null || echo "Failed to get repositories"

echo "Tags for ${TEST_IMAGE}:"
curl -s http://${REGISTRY}/v2/${TEST_IMAGE}/tags/list | jq -r '.tags[]?' 2>/dev/null || echo "Failed to get tags"
echo ""

# Cleanup
echo "🧹 Cleaning up test image..."
docker rmi ${FULL_IMAGE_NAME} >/dev/null 2>&1 || true
rm -f /tmp/Dockerfile.test
echo "✅ Cleanup completed"
echo ""

echo "🎉 Local registry test completed successfully!"
echo "Your local registry at ${REGISTRY} is working correctly." 